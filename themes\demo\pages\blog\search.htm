##
url = "/blog/search"
layout = "default"
title = "Search Blog Posts"
meta_title = "Search - Blog"

[collection blog]
handle = "Blog\Post"

[resources]
vars[activeNavLink] = 'blog'
==
{% set searchTerm = get('term')|trim %}
{% set posts = blog.searchWhere(searchTerm, ['title', 'content']).paginate(16) %}
{% put pageTitle=searchTerm ~ ' - Search Results' %}

<div class="container">
    <header>
        <h1>{{ searchTerm }} - Search Results</h1>
    </header>
    <div class="row">
        <div class="col-md-12">
            <form class="mb-5">
                <div class="input-group">
                    <div class="form-control-search">
                        <input type="text" name="term" class="form-control" placeholder="Search" value="{{ searchTerm }}">
                        <span class="search-icon"></span>
                    </div>
                </div>
            </form>

            <div class="blog-featured row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-4 g-3 pb-5">
                {% for post in posts %}
                    {% partial 'blog/post-card' post=post %}
                {% endfor %}
            </div>
            <nav class="blog-pagination" aria-label="Pagination">
                {{ pager(posts, { withQuery: true }) }}
            </nav>
        </div>
    </div>
</div>
