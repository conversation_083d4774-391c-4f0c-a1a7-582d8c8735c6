{% set parent = article.parent %}

<ul class="bullet-list list-content">
    {% if parent %}
        {% set prevLink = null %}
        {% set nextLink = null %}
        {% for sibling in parent.children %}
            {% if nextLink == true %}
                <li class="mb-1">
                    Next: <a href="{{ 'wiki/article'|page({ fullslug: sibling.fullslug, id: sibling.id }) }}">{{ sibling.title }}</a>
                </li>
                {% set nextLink = null %}
            {% endif %}

            {% if sibling.id == article.id %}
                {% if prevLink %}
                    <li class="mb-1">
                        Previous: <a href="{{ 'wiki/article'|page({ fullslug: prevLink.fullslug, id: prevLink.id }) }}">{{ prevLink.title }}</a>
                    </li>
                {% endif %}
                {% set nextLink = true %}
            {% endif %}
            {% set prevLink = sibling %}
        {% endfor %}

        <li class="mb-1">
            Return to <a href="{{ 'wiki/article'|page({ fullslug: parent.fullslug, id: parent.id }) }}">{{ parent.title }}</a>
        </li>
    {% endif %}
</ul>
