##
url = "/wiki/:fullslug*/:id"
layout = "wiki"
title = "Wiki Article"
meta_title = "{{ wiki.title }}"

[section wiki]
handle = "Page\Article"
identifier = "id"

[resources]
vars[activeNavLink] = 'wiki'
==
{% set article = wiki %}
{% if article is empty %}
    {% do abort(404) %}
{% elseif article.fullslug and article.fullslug != this.param.fullslug %}
    {% do redirect(''|page({ fullslug: article.fullslug }), 301) %}
{% endif %}

<div class="container">

    <div class="py-3">
        {% partial 'wiki/breadcrumb' article=article %}
    </div>

    <article class="wiki-article pb-3">
        <h1 class="wiki-article-title mb-3">{{ article.title }}</h1>
        <p class="lead">{{ article.summary_text }}</p>

        <div class="wiki-banner mb-3">
            {% if article.banner %}
                <div class="text-banner" style="background-image:url('{{ article.banner.path }}')"></div>
            {% else %}
                <div class="text-banner" style="background-image:url('{{ partial('site/helpers/random-stock-image') }}')"></div>
            {% endif %}
        </div>

        {{ article.content|content }}

        <div class="wiki-article-gallery mb-3">
            {% partial 'controls/gallery-slider' gallery=article.gallery %}
        </div>
    </article>

    {% partial 'wiki/continue' article=article %}

    {% if article.external_links %}
        <hr />
        <h2>External Links</h2>
        <ul class="bullet-list list-content">
            {% for link in article.external_links %}
                <li><a href="{{ link.link_url }}">{{ link.link_text }}</a></li>
            {% endfor %}
        </ul>
    {% endif %}

</div>
