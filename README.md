<p align="center">
    <img src="https://github.com/octobercms/october/blob/develop/themes/demo/assets/images/favicon.png?raw=true" alt="October" width="25%" height="25%" />
</p>

[October](https://octobercms.com) is a Content Management System (CMS) and web platform whose sole purpose is to make your development workflow simple again. It was born out of frustration with existing systems. We feel building websites has become a convoluted and confusing process that leaves developers unsatisfied. We want to turn you around to the simpler side and get back to basics.

October's mission is to show the world that web development is not rocket science.

[![Build Status](https://github.com/octobercms/library/actions/workflows/tests.yml/badge.svg)](https://octobercms.com/)
[![Downloads](https://img.shields.io/packagist/dt/october/rain)](https://docs.octobercms.com/)
[![Version](https://img.shields.io/packagist/v/october/october)](https://octobercms.com/changelog)
[![License](https://poser.pugx.org/october/october/license.svg)](./LICENSE.md)

> *Please note*: October is open source but it is not free software. A license with a small fee is required for each website you build with October CMS.

## Installing October

Instructions on how to install October can be found at the [installation guide](https://docs.octobercms.com/3.x/setup/installation.html).

### Quick Start Installation

If you have composer installed, run this in your terminal to install October CMS from command line. This will place the files in a directory named **myoctober**.

    composer create-project october/october myoctober

If you plan on using a database, run this command inside the application directory.

    php artisan october:install

## Learning October

The best place to learn October CMS is by [reading the documentation](https://docs.octobercms.com) or [following some tutorials](https://octobercms.com/support/articles/tutorials).

You may also watch this [introductory video](https://www.youtube.com/watch?v=yLZTOeOS7wI). Make sure to check out our [official YouTube channel](https://www.youtube.com/c/OctoberCMSOfficial). There is also the excellent video series by [Watch & Learn](https://watch-learn.com/series/making-websites-with-october-cms).

For code examples of building with October CMS, visit the [RainLab Plugin Suite](https://github.com/rainlab) or the [October Demos Repo](https://github.com/octoberdemos).

## Coding Standards

Please follow the following guides and code standards:

* [PSR 4 Coding Standards](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-4-autoloader.md)
* [PSR 2 Coding Style Guide](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md)
* [PSR 1 Coding Standards](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-1-basic-coding-standard.md)

## Security Vulnerabilities

Please review [our security policy](https://github.com/octobercms/october/security/policy) on how to report security vulnerabilities.

## Development Team

October CMS was created by [Alexey Bobkov](https://www.linkedin.com/in/alexey-bobkov-232ba02b/) and [Samuel Georges](https://www.linkedin.com/in/samuel-georges-0a964131/), who both continue to develop the platform.

## Foundation library

The CMS uses [Laravel](https://laravel.com) as a foundation PHP framework.

## Contact

For announcements and updates:

* [Contact Us Page](http://octoberdev.test/contact)
* [Follow us on Twitter](https://twitter.com/octobercms)
* [Like us on Facebook](https://facebook.com/octobercms)

To chat or hang out:

* [Join us on Slack](https://join.slack.com/t/octobercms/shared_invite/zt-2f19m689c-VCrBPc2P1dmqAJ_86Y8e_Q)
* [Join us on Discord](https://discord.gg/gEKgwSZ)
* [Join us on Telegram](https://t.me/octoberchat)

## License

The October CMS platform is licensed software, see [End User License Agreement](./LICENSE.md) (EULA) for more details.
