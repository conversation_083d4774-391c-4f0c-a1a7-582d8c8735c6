/*!
  * PhotoSwipe Lightbox 5.2.7 - https://photoswipe.com
  * (c) 2022 <PERSON><PERSON><PERSON>
  */
function t(t,i,s){const h=document.createElement(i||"div");return t&&(h.className=t),s&&s.appendChild(h),h}function i(t,i,s){t.style.width="number"==typeof i?i+"px":i,t.style.height="number"==typeof s?s+"px":s}const s="idle",h="loading",e="loaded",n="error";function o(t,i,s=document){let h=[];if(t instanceof Element)h=[t];else if(t instanceof NodeList||Array.isArray(t))h=Array.from(t);else{const e="string"==typeof t?t:i;e&&(h=Array.from(s.querySelectorAll(e)))}return h}class r{constructor(t,i){this.type=t,i&&Object.assign(this,i)}preventDefault(){this.defaultPrevented=!0}}class a{constructor(i,s){this.element=t("pswp__img pswp__img--placeholder",i?"img":"",s),i&&(this.element.decoding="async",this.element.alt="",this.element.src=i,this.element.setAttribute("role","presentation")),this.element.setAttribute("aria-hiden","true")}setDisplayedSize(t,s){this.element&&("IMG"===this.element.tagName?(i(this.element,250,"auto"),this.element.style.transformOrigin="0 0",this.element.style.transform=function(t,i,s){let h="translate3d("+t+"px,"+(i||0)+"px,0)";return void 0!==s&&(h+=" scale3d("+s+","+s+",1)"),h}(0,0,t/250)):i(this.element,t,s))}destroy(){this.element.parentNode&&this.element.remove(),this.element=null}}class c{constructor(t,i,h){this.instance=i,this.data=t,this.index=h,this.element=void 0,this.width=Number(this.data.w)||Number(this.data.width)||0,this.height=Number(this.data.h)||Number(this.data.height)||0,this.isAttached=!1,this.hasSlide=!1,this.state=s,this.data.type?this.type=this.data.type:this.data.src?this.type="image":this.type="html",this.instance.dispatch("contentInit",{content:this})}removePlaceholder(){this.placeholder&&!this.keepPlaceholder()&&setTimeout((()=>{this.placeholder&&(this.placeholder.destroy(),this.placeholder=null)}),500)}load(i,s){if(!this.placeholder&&this.slide&&this.usePlaceholder()){const t=this.instance.applyFilters("placeholderSrc",!(!this.data.msrc||!this.slide.isFirstSlide)&&this.data.msrc,this);this.placeholder=new a(t,this.slide.container)}this.element&&!s||this.instance.dispatch("contentLoad",{content:this,isLazy:i}).defaultPrevented||(this.isImageContent()?this.loadImage(i):(this.element=t("pswp__content"),this.element.innerHTML=this.data.html||""),s&&this.slide&&this.slide.updateContentSize(!0))}loadImage(i){const s=t("pswp__img","img");this.element=s,this.instance.dispatch("contentLoadImage",{content:this,isLazy:i}).defaultPrevented||(this.data.srcset&&(s.srcset=this.data.srcset),s.src=this.data.src,s.alt=this.data.alt||"",this.state=h,s.complete?this.onLoaded():(s.onload=()=>{this.onLoaded()},s.onerror=()=>{this.onError()}))}setSlide(t){this.slide=t,this.hasSlide=!0,this.instance=t.pswp}onLoaded(){this.state=e,this.slide&&(this.instance.dispatch("loadComplete",{slide:this.slide,content:this}),this.slide.isActive&&this.slide.heavyAppended&&!this.element.parentNode&&(this.slide.container.innerHTML="",this.append(),this.slide.updateContentSize(!0)))}onError(){this.state=n,this.slide&&(this.displayError(),this.instance.dispatch("loadComplete",{slide:this.slide,isError:!0,content:this}),this.instance.dispatch("loadError",{slide:this.slide,content:this}))}isLoading(){return this.instance.applyFilters("isContentLoading",this.state===h,this)}isError(){return this.state===n}isImageContent(){return"image"===this.type}setDisplayedSize(t,s){if(this.element&&(this.placeholder&&this.placeholder.setDisplayedSize(t,s),!this.instance.dispatch("contentResize",{content:this,width:t,height:s}).defaultPrevented&&(i(this.element,t,s),this.isImageContent()&&!this.isError()))){const i=this.element;i.srcset&&(!i.dataset.largestUsedSize||t>parseInt(i.dataset.largestUsedSize,10))&&(i.sizes=t+"px",i.dataset.largestUsedSize=String(t)),this.slide&&this.instance.dispatch("imageSizeChange",{slide:this.slide,width:t,height:s,content:this})}}isZoomable(){return this.instance.applyFilters("isContentZoomable",this.isImageContent()&&this.state!==n,this)}usePlaceholder(){return this.instance.applyFilters("useContentPlaceholder",this.isImageContent(),this)}lazyLoad(){this.instance.dispatch("contentLazyLoad",{content:this}).defaultPrevented||this.load(!0)}keepPlaceholder(){return this.instance.applyFilters("isKeepingPlaceholder",this.isLoading(),this)}destroy(){this.hasSlide=!1,this.slide=null,this.instance.dispatch("contentDestroy",{content:this}).defaultPrevented||(this.remove(),this.isImageContent()&&this.element&&(this.element.onload=null,this.element.onerror=null,this.element=null))}displayError(){if(this.slide){let i=t("pswp__error-msg");i.innerText=this.instance.options.errorMsg,i=this.instance.applyFilters("contentErrorElement",i,this),this.element=t("pswp__content pswp__error-msg-container"),this.element.appendChild(i),this.slide.container.innerHTML="",this.slide.container.appendChild(this.element),this.slide.updateContentSize(!0),this.removePlaceholder()}}append(){this.isAttached=!0,this.state!==n?this.instance.dispatch("contentAppend",{content:this}).defaultPrevented||(this.isImageContent()?this.slide&&!this.slide.isActive&&"decode"in this.element?(this.isDecoding=!0,requestAnimationFrame((()=>{this.element&&"IMG"===this.element.tagName&&this.element.decode().then((()=>{this.isDecoding=!1,requestAnimationFrame((()=>{this.appendImage()}))})).catch((()=>{this.isDecoding=!1}))}))):(!this.placeholder||this.state!==e&&this.state!==n||this.removePlaceholder(),this.appendImage()):this.element&&!this.element.parentNode&&this.slide.container.appendChild(this.element)):this.displayError()}activate(){this.instance.dispatch("contentActivate",{content:this}).defaultPrevented||this.slide&&(this.isImageContent()&&this.isDecoding?this.appendImage():this.isError()&&this.load(!1,!0))}deactivate(){this.instance.dispatch("contentDeactivate",{content:this})}remove(){this.isAttached=!1,this.instance.dispatch("contentRemove",{content:this}).defaultPrevented||this.element&&this.element.parentNode&&this.element.remove()}appendImage(){this.isAttached&&(this.instance.dispatch("contentAppendImage",{content:this}).defaultPrevented||this.slide&&this.element&&!this.element.parentNode&&(this.slide.container.appendChild(this.element),!this.placeholder||this.state!==e&&this.state!==n||this.removePlaceholder()))}}function l(t,i,s,h,e){let n;if(i.paddingFn)n=i.paddingFn(s,h,e)[t];else if(i.padding)n=i.padding[t];else{const s="padding"+t[0].toUpperCase()+t.slice(1);i[s]&&(n=i[s])}return n||0}class u{constructor(t,i,s,h){this.pswp=h,this.options=t,this.itemData=i,this.index=s}update(t,i,s){this.elementSize={x:t,y:i},this.panAreaSize=s;const h=this.panAreaSize.x/this.elementSize.x,e=this.panAreaSize.y/this.elementSize.y;this.fit=Math.min(1,h<e?h:e),this.fill=Math.min(1,h>e?h:e),this.vFill=Math.min(1,e),this.initial=this.t(),this.secondary=this.i(),this.max=Math.max(this.initial,this.secondary,this.o()),this.min=Math.min(this.fit,this.initial,this.secondary),this.pswp&&this.pswp.dispatch("zoomLevelsUpdate",{zoomLevels:this,slideData:this.itemData})}l(t){const i=t+"ZoomLevel",s=this.options[i];if(s)return"function"==typeof s?s(this):"fill"===s?this.fill:"fit"===s?this.fit:Number(s)}i(){let t=this.l("secondary");return t||(t=Math.min(1,3*this.fit),t*this.elementSize.x>4e3&&(t=4e3/this.elementSize.x),t)}t(){return this.l("initial")||this.fit}o(){const t=this.l("max");return t||Math.max(1,4*this.fit)}}function d(t,i,s){const h=i.createContentFromData(t,s);if(!h||!h.lazyLoad)return;const{options:e}=i,n=i.viewportSize||function(t,i){if(t.getViewportSizeFn){const s=t.getViewportSizeFn(t,i);if(s)return s}return{x:document.documentElement.clientWidth,y:window.innerHeight}}(e,i),o=function(t,i,s,h){return{x:i.x-l("left",t,i,s,h)-l("right",t,i,s,h),y:i.y-l("top",t,i,s,h)-l("bottom",t,i,s,h)}}(e,n,t,s),r=new u(e,t,-1);return r.update(h.width,h.height,o),h.lazyLoad(),h.setDisplayedSize(Math.ceil(h.width*r.initial),Math.ceil(h.height*r.initial)),h}class m extends class extends class{constructor(){this.u={},this.m={},this.pswp=void 0,this.options=void 0}addFilter(t,i,s=100){this.m[t]||(this.m[t]=[]),this.m[t].push({fn:i,priority:s}),this.m[t].sort(((t,i)=>t.priority-i.priority)),this.pswp&&this.pswp.addFilter(t,i,s)}removeFilter(t,i){this.m[t]&&(this.m[t]=this.m[t].filter((t=>t.fn!==i))),this.pswp&&this.pswp.removeFilter(t,i)}applyFilters(t,...i){return this.m[t]&&this.m[t].forEach((t=>{i[0]=t.fn.apply(this,i)})),i[0]}on(t,i){this.u[t]||(this.u[t]=[]),this.u[t].push(i),this.pswp&&this.pswp.on(t,i)}off(t,i){this.u[t]&&(this.u[t]=this.u[t].filter((t=>i!==t))),this.pswp&&this.pswp.off(t,i)}dispatch(t,i){if(this.pswp)return this.pswp.dispatch(t,i);const s=new r(t,i);return this.u?(this.u[t]&&this.u[t].forEach((t=>{t.call(this,s)})),s):s}}{getNumItems(){let t;const{dataSource:i}=this.options;i?"length"in i?t=i.length:"gallery"in i&&(i.items||(i.items=this.p(i.gallery)),i.items&&(t=i.items.length)):t=0;const s=this.dispatch("numItems",{dataSource:i,numItems:t});return this.applyFilters("numItems",s.numItems,i)}createContentFromData(t,i){return new c(t,this,i)}getItemData(t){const{dataSource:i}=this.options;let s;Array.isArray(i)?s=i[t]:i&&i.gallery&&(i.items||(i.items=this.p(i.gallery)),s=i.items[t]);let h=s;h instanceof Element&&(h=this.g(h));const e=this.dispatch("itemData",{itemData:h||{},index:t});return this.applyFilters("itemData",e.itemData,t)}p(t){return this.options.children||this.options.childSelector?o(this.options.children,this.options.childSelector,t)||[]:[t]}g(t){const i={element:t},s="A"===t.tagName?t:t.querySelector("a");if(s){i.src=s.dataset.pswpSrc||s.href,s.dataset.pswpSrcset&&(i.srcset=s.dataset.pswpSrcset),i.width=parseInt(s.dataset.pswpWidth,10),i.height=parseInt(s.dataset.pswpHeight,10),i.w=i.width,i.h=i.height,s.dataset.pswpType&&(i.type=s.dataset.pswpType);const h=t.querySelector("img");h&&(i.msrc=h.currentSrc||h.src,i.alt=h.getAttribute("alt")),(s.dataset.pswpCropped||s.dataset.cropped)&&(i.thumbCropped=!0)}return this.applyFilters("domItemData",i,t,s)}}{constructor(t){super(),this.options=t||{},this.I=0}init(){this.onThumbnailsClick=this.onThumbnailsClick.bind(this),o(this.options.gallery,this.options.gallerySelector).forEach((t=>{t.addEventListener("click",this.onThumbnailsClick,!1)}))}onThumbnailsClick(t){if(function(t){if(2===t.which||t.ctrlKey||t.metaKey||t.altKey||t.shiftKey)return!0}(t)||window.pswp||!1===window.navigator.onLine)return;let i={x:t.clientX,y:t.clientY};i.x||i.y||(i=null);let s=this.getClickedIndex(t);s=this.applyFilters("clickedIndex",s,t,this);const h={gallery:t.currentTarget};s>=0&&(t.preventDefault(),this.loadAndOpen(s,h,i))}getClickedIndex(t){if(this.options.getClickedIndexFn)return this.options.getClickedIndexFn.call(this,t);const i=t.target,s=o(this.options.children,this.options.childSelector,t.currentTarget).findIndex((t=>t===i||t.contains(i)));return-1!==s?s:this.options.children||this.options.childSelector?-1:0}loadAndOpen(t,i,s){return!window.pswp&&(this.options.index=t,this.options.initialPointerPos=s,this.shouldOpen=!0,this.preload(t,i),!0)}preload(t,i){const{options:s}=this;i&&(s.dataSource=i);const h=[],e=typeof s.pswpModule;if("function"==typeof(n=s.pswpModule)&&n.prototype&&n.prototype.goTo)h.push(Promise.resolve(s.pswpModule));else{if("string"===e)throw new Error("pswpModule as string is no longer supported");if("function"!==e)throw new Error("pswpModule is not valid");h.push(s.pswpModule())}var n;"function"==typeof s.openPromise&&h.push(s.openPromise()),!1!==s.preloadFirstSlide&&t>=0&&(this._=function(t,i){const s=i.getItemData(t);if(!i.dispatch("lazyLoadSlide",{index:t,itemData:s}).defaultPrevented)return d(s,i,t)}(t,this));const o=++this.I;Promise.all(h).then((t=>{if(this.shouldOpen){const i=t[0];this.v(i,o)}}))}v(t,i){if(i!==this.I&&this.shouldOpen)return;if(this.shouldOpen=!1,window.pswp)return;const s="object"==typeof t?new t.default(this.options):new t(this.options);this.pswp=s,window.pswp=s,Object.keys(this.u).forEach((t=>{this.u[t].forEach((i=>{s.on(t,i)}))})),Object.keys(this.m).forEach((t=>{this.m[t].forEach((i=>{s.addFilter(t,i.fn,i.priority)}))})),this._&&(s.contentLoader.addToCache(this._),this._=null),s.on("destroy",(()=>{this.pswp=null,window.pswp=null})),s.init()}destroy(){this.pswp&&this.pswp.destroy(),this.shouldOpen=!1,this.u=null,o(this.options.gallery,this.options.gallerySelector).forEach((t=>{t.removeEventListener("click",this.onThumbnailsClick,!1)}))}}export{m as default};
