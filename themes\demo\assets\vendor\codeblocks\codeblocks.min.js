(()=>{var e,t={830:(e,t,r)=>{"use strict";const n=jQuery;var i=r.n(n),o=r(631),a=r.n(o),l=r(379),s=r.n(l),c=r(789),u={insert:"head",singleton:!1};s()(c.Z,u);c.Z.locals;var d=r(845),f={insert:"head",singleton:!1};s()(d.Z,f);d.Z.locals;r(702),r(959),r(762),r(589),r(93);i()(document).on("render",(function(){i()(".code-block > pre").each((function(){if(!this.dataset.disposable){this.dataset.disposable=!0;var e,t=i()(this),r=t.text();e="php"===t.data("language")?"text/x-php":{name:"twig",base:"text/html"},t.empty(),new(a())(this,{value:r,mode:e,lineNumbers:!0,readOnly:!0})}}))})),i()(document).on("click",".expand-code",(function(){i()(this).closest(".collapsed-code-block").removeClass("collapsed")}))},93:(e,t,r)=>{!function(e){"use strict";e.multiplexingMode=function(t){var r=Array.prototype.slice.call(arguments,1);function n(e,t,r,n){if("string"==typeof t){var i=e.indexOf(t,r);return n&&i>-1?i+t.length:i}var o=t.exec(r?e.slice(r):e);return o?o.index+r+(n?o[0].length:0):-1}return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null,startingInner:!1}},copyState:function(r){return{outer:e.copyState(t,r.outer),innerActive:r.innerActive,inner:r.innerActive&&e.copyState(r.innerActive.mode,r.inner),startingInner:r.startingInner}},token:function(i,o){if(o.innerActive){var a=o.innerActive;if(c=i.string,!a.close&&i.sol())return o.innerActive=o.inner=null,this.token(i,o);if((d=a.close&&!o.startingInner?n(c,a.close,i.pos,a.parseDelimiters):-1)==i.pos&&!a.parseDelimiters)return i.match(a.close),o.innerActive=o.inner=null,a.delimStyle&&a.delimStyle+" "+a.delimStyle+"-close";d>-1&&(i.string=c.slice(0,d));var l=a.mode.token(i,o.inner);return d>-1?i.string=c:i.pos>i.start&&(o.startingInner=!1),d==i.pos&&a.parseDelimiters&&(o.innerActive=o.inner=null),a.innerStyle&&(l=l?l+" "+a.innerStyle:a.innerStyle),l}for(var s=1/0,c=i.string,u=0;u<r.length;++u){var d,f=r[u];if((d=n(c,f.open,i.pos))==i.pos){f.parseDelimiters||i.match(f.open),o.startingInner=!!f.parseDelimiters,o.innerActive=f;var p=0;if(t.indent){var h=t.indent(o.outer,"","");h!==e.Pass&&(p=h)}return o.inner=e.startState(f.mode,p),f.delimStyle&&f.delimStyle+" "+f.delimStyle+"-open"}-1!=d&&d<s&&(s=d)}s!=1/0&&(i.string=c.slice(0,s));var m=t.token(i,o.outer);return s!=1/0&&(i.string=c),m},indent:function(r,n,i){var o=r.innerActive?r.innerActive.mode:t;return o.indent?o.indent(r.innerActive?r.inner:r.outer,n,i):e.Pass},blankLine:function(n){var i=n.innerActive?n.innerActive.mode:t;if(i.blankLine&&i.blankLine(n.innerActive?n.inner:n.outer),n.innerActive)"\n"===n.innerActive.close&&(n.innerActive=n.inner=null);else for(var o=0;o<r.length;++o){var a=r[o];"\n"===a.open&&(n.innerActive=a,n.inner=e.startState(a.mode,i.indent?i.indent(n.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}(r(631))},631:function(e){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=n||i||o,l=a&&(n?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),c=s&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\/(\d+)/.exec(e),d=u&&+u[1],f=/Opera\//.test(e),p=/Apple Computer/.test(navigator.vendor),h=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),m=/PhantomJS/.test(e),g=p&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),v=/Android/.test(e),y=g||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=g||/Mac/.test(t),w=/\bCrOS\b/.test(e),x=/win/i.test(t),k=f&&e.match(/Version\/(\d*\.\d*)/);k&&(k=Number(k[1])),k&&k>=15&&(f=!1,s=!0);var _=b&&(c||f&&(null==k||k<12.11)),C=r||a&&l>=9;function S(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var M,T=function(e,t){var r=e.className,n=S(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function L(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function N(e,t){return L(e).appendChild(t)}function A(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function O(e,t,r,n){var i=A(e,t,r,n);return i.setAttribute("role","presentation"),i}function z(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function E(e,t){var r=e.className;S(t).test(r)||(e.className+=(r?" ":"")+t)}function I(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!S(r[n]).test(t)&&(t+=" "+r[n]);return t}M=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function W(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function q(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function F(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=r-a%r,o=l+1}}g?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(P=function(e){try{e.select()}catch(e){}});var H=function(){this.id=null,this.f=null,this.time=0,this.handler=W(this.onTimeout,this)};function R(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}H.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},H.prototype.set=function(e,t){this.f=t;var r=+new Date+e;(!this.id||r<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=r)};var B=50,j={toString:function(){return"CodeMirror.Pass"}},U={scroll:!1},K={origin:"*mouse"},V={origin:"+move"};function $(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||i+a>=t)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var G=[""];function X(e){for(;G.length<=e;)G.push(Y(G)+" ");return G[e]}function Y(e){return e[e.length-1]}function Z(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function Q(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}function J(){}function ee(e,t){var r;return Object.create?r=Object.create(e):(J.prototype=e,r=new J),t&&q(t,r),r}var te=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function re(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||te.test(e))}function ne(e,t){return t?!!(t.source.indexOf("\\w")>-1&&re(e))||t.test(e):re(e)}function ie(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var oe=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ae(e){return e.charCodeAt(0)>=768&&oe.test(e)}function le(e,t,r){for(;(r<0?t>0:t<e.length)&&ae(e.charAt(t));)t+=r;return t}function se(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function ce(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}var ue=null;function de(e,t,r){var n;ue=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:ue=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:ue=i)}return null!=n?n:ue}var fe=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function r(r){return r<=247?e.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?t.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L"}var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,a=/[Lb1n]/,l=/[1n]/;function s(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var c="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!n.test(e))return!1;for(var u=e.length,d=[],f=0;f<u;++f)d.push(r(e.charCodeAt(f)));for(var p=0,h=c;p<u;++p){var m=d[p];"m"==m?d[p]=h:h=m}for(var g=0,v=c;g<u;++g){var y=d[g];"1"==y&&"r"==v?d[g]="n":o.test(y)&&(v=y,"r"==y&&(d[g]="R"))}for(var b=1,w=d[0];b<u-1;++b){var x=d[b];"+"==x&&"1"==w&&"1"==d[b+1]?d[b]="1":","!=x||w!=d[b+1]||"1"!=w&&"n"!=w||(d[b]=w),w=x}for(var k=0;k<u;++k){var _=d[k];if(","==_)d[k]="N";else if("%"==_){var C=void 0;for(C=k+1;C<u&&"%"==d[C];++C);for(var S=k&&"!"==d[k-1]||C<u&&"1"==d[C]?"1":"N",M=k;M<C;++M)d[M]=S;k=C-1}}for(var T=0,L=c;T<u;++T){var N=d[T];"L"==L&&"1"==N?d[T]="L":o.test(N)&&(L=N)}for(var A=0;A<u;++A)if(i.test(d[A])){var O=void 0;for(O=A+1;O<u&&i.test(d[O]);++O);for(var z="L"==(A?d[A-1]:c),D=z==("L"==(O<u?d[O]:c))?z?"L":"R":c,E=A;E<O;++E)d[E]=D;A=O-1}for(var I,P=[],W=0;W<u;)if(a.test(d[W])){var q=W;for(++W;W<u&&a.test(d[W]);++W);P.push(new s(0,q,W))}else{var F=W,H=P.length,R="rtl"==t?1:0;for(++W;W<u&&"L"!=d[W];++W);for(var B=F;B<W;)if(l.test(d[B])){F<B&&(P.splice(H,0,new s(1,F,B)),H+=R);var j=B;for(++B;B<W&&l.test(d[B]);++B);P.splice(H,0,new s(2,j,B)),H+=R,F=B}else++B;F<W&&P.splice(H,0,new s(1,F,W))}return"ltr"==t&&(1==P[0].level&&(I=e.match(/^\s+/))&&(P[0].from=I[0].length,P.unshift(new s(0,0,I[0].length))),1==Y(P).level&&(I=e.match(/\s+$/))&&(Y(P).to-=I[0].length,P.push(new s(0,u-I[0].length,u)))),"rtl"==t?P.reverse():P}}();function pe(e,t){var r=e.order;return null==r&&(r=e.order=fe(e.text,t)),r}var he=[],me=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||he).concat(r)}};function ge(e,t){return e._handlers&&e._handlers[t]||he}function ve(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=R(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function ye(e,t){var r=ge(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function be(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ye(e,r||t.type,e,t),Se(t)||t.codemirrorIgnore}function we(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==R(r,t[n])&&r.push(t[n])}function xe(e,t){return ge(e,t).length>0}function ke(e){e.prototype.on=function(e,t){me(this,e,t)},e.prototype.off=function(e,t){ve(this,e,t)}}function _e(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Ce(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Se(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Me(e){_e(e),Ce(e)}function Te(e){return e.target||e.srcElement}function Le(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var Ne,Ae,Oe=function(){if(a&&l<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function ze(e){if(null==Ne){var t=A("span","​");N(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Ne=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&l<8))}var r=Ne?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function De(e){if(null!=Ae)return Ae;var t=N(e,document.createTextNode("AخA")),r=M(t,0,1).getBoundingClientRect(),n=M(t,1,2).getBoundingClientRect();return L(e),!(!r||r.left==r.right)&&(Ae=n.right-r.right<3)}var Ee,Ie=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},Pe=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},We="oncopy"in(Ee=A("div"))||(Ee.setAttribute("oncopy","return;"),"function"==typeof Ee.oncopy),qe=null;function Fe(e){if(null!=qe)return qe;var t=N(e,A("span","x")),r=t.getBoundingClientRect(),n=M(t,0,1).getBoundingClientRect();return qe=Math.abs(r.left-n.left)>1}var He={},Re={};function Be(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),He[e]=t}function je(e,t){Re[e]=t}function Ue(e){if("string"==typeof e&&Re.hasOwnProperty(e))e=Re[e];else if(e&&"string"==typeof e.name&&Re.hasOwnProperty(e.name)){var t=Re[e.name];"string"==typeof t&&(t={name:t}),(e=ee(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ue("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ue("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Ke(e,t){t=Ue(t);var r=He[t.name];if(!r)return Ke(e,"text/plain");var n=r(e,t);if(Ve.hasOwnProperty(t.name)){var i=Ve[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var Ve={};function $e(e,t){q(t,Ve.hasOwnProperty(e)?Ve[e]:Ve[e]={})}function Ge(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Xe(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Ye(e,t,r){return!e.startState||e.startState(t,r)}var Ze=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function Qe(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function Je(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function et(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function tt(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function rt(e){if(null==e.parent)return null;for(var t=e.parent,r=R(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function nt(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return r+a}function it(e,t){return t>=e.first&&t<e.first+e.size}function ot(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function at(e,t,r){if(void 0===r&&(r=null),!(this instanceof at))return new at(e,t,r);this.line=e,this.ch=t,this.sticky=r}function lt(e,t){return e.line-t.line||e.ch-t.ch}function st(e,t){return e.sticky==t.sticky&&0==lt(e,t)}function ct(e){return at(e.line,e.ch)}function ut(e,t){return lt(e,t)<0?t:e}function dt(e,t){return lt(e,t)<0?e:t}function ft(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function pt(e,t){if(t.line<e.first)return at(e.first,0);var r=e.first+e.size-1;return t.line>r?at(r,Qe(e,r).text.length):ht(t,Qe(e,t.line).text.length)}function ht(e,t){var r=e.ch;return null==r||r>t?at(e.line,t):r<0?at(e.line,0):e}function mt(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=pt(e,t[n]);return r}Ze.prototype.eol=function(){return this.pos>=this.string.length},Ze.prototype.sol=function(){return this.pos==this.lineStart},Ze.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Ze.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Ze.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Ze.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Ze.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Ze.prototype.skipToEnd=function(){this.pos=this.string.length},Ze.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Ze.prototype.backUp=function(e){this.pos-=e},Ze.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=F(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?F(this.string,this.lineStart,this.tabSize):0)},Ze.prototype.indentation=function(){return F(this.string,null,this.tabSize)-(this.lineStart?F(this.string,this.lineStart,this.tabSize):0)},Ze.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Ze.prototype.current=function(){return this.string.slice(this.start,this.pos)},Ze.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Ze.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Ze.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var gt=function(e,t){this.state=e,this.lookAhead=t},vt=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function yt(e,t,r,n){var i=[e.state.modeGen],o={};Tt(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var a=r.state,l=function(n){r.baseTokens=i;var l=e.state.overlays[n],s=1,c=0;r.state=!0,Tt(e,t.text,l.mode,r,(function(e,t){for(var r=s;c<e;){var n=i[s];n>e&&i.splice(s,1,e,i[s+1],n),s+=2,c=Math.min(e,n)}if(t)if(l.opaque)i.splice(r,s-r,e,"overlay "+t),s=r+2;else for(;r<s;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}}),o),r.state=a,r.baseTokens=null,r.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)l(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function bt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=wt(e,rt(t)),i=t.text.length>e.options.maxHighlightLength&&Ge(e.doc.mode,n.state),o=yt(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function wt(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new vt(n,!0,t);var o=Lt(e,t,r),a=o>n.first&&Qe(n,o-1).stateAfter,l=a?vt.fromSaved(n,a,o):new vt(n,Ye(n.mode),o);return n.iter(o,t,(function(r){xt(e,r.text,l);var n=l.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?l.save():null,l.nextLine()})),r&&(n.modeFrontier=l.line),l}function xt(e,t,r,n){var i=e.doc.mode,o=new Ze(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&kt(i,r.state);!o.eol();)_t(i,o,r.state),o.start=o.pos}function kt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Xe(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function _t(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Xe(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}vt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},vt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},vt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},vt.fromSaved=function(e,t,r){return t instanceof gt?new vt(e,Ge(e.mode,t.state),r,t.lookAhead):new vt(e,Ge(e.mode,t),r)},vt.prototype.save=function(e){var t=!1!==e?Ge(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new gt(t,this.maxLookAhead):t};var Ct=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function St(e,t,r,n){var i,o,a=e.doc,l=a.mode,s=Qe(a,(t=pt(a,t)).line),c=wt(e,t.line,r),u=new Ze(s.text,e.options.tabSize,c);for(n&&(o=[]);(n||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=_t(l,u,c.state),n&&o.push(new Ct(u,i,Ge(a.mode,c.state)));return n?o:new Ct(u,i,c.state)}function Mt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|\\s)"+r[2]+"(?:$|\\s)").test(t[n])||(t[n]+=" "+r[2])}return e}function Tt(e,t,r,n,i,o,a){var l=r.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,c=0,u=null,d=new Ze(t,e.options.tabSize,n),f=e.options.addModeClass&&[null];for(""==t&&Mt(kt(r,n.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(l=!1,a&&xt(e,t,n,d.pos),d.pos=t.length,s=null):s=Mt(_t(r,d,n.state,f),o),f){var p=f[0].name;p&&(s="m-"+(s?p+" "+s:p))}if(!l||u!=s){for(;c<d.start;)i(c=Math.min(d.start,c+5e3),u);u=s}d.start=d.pos}for(;c<d.pos;){var h=Math.min(d.pos,c+5e3);i(h,u),c=h}}function Lt(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=Qe(o,l-1),c=s.stateAfter;if(c&&(!r||l+(c instanceof gt?c.lookAhead:0)<=o.modeFrontier))return l;var u=F(s.text,null,e.options.tabSize);(null==i||n>u)&&(i=l-1,n=u)}return i}function Nt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=Qe(e,n).stateAfter;if(i&&(!(i instanceof gt)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}}var At=!1,Ot=!1;function zt(){At=!0}function Dt(){Ot=!0}function Et(e,t,r){this.marker=e,this.from=t,this.to=r}function It(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function Pt(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Wt(e,t,r){var n=r&&window.WeakSet&&(r.markedSpans||(r.markedSpans=new WeakSet));n&&e.markedSpans&&n.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],n&&n.add(e.markedSpans)),t.marker.attachLine(e)}function qt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new Et(a,o.from,l?null:o.to))}}return n}function Ft(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new Et(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return n}function Ht(e,t){if(t.full)return null;var r=it(e,t.from.line)&&Qe(e,t.from.line).markedSpans,n=it(e,t.to.line)&&Qe(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==lt(t.from,t.to),l=qt(r,i,a),s=Ft(n,o,a),c=1==t.text.length,u=Y(t.text).length+(c?i:0);if(l)for(var d=0;d<l.length;++d){var f=l[d];if(null==f.to){var p=It(s,f.marker);p?c&&(f.to=null==p.to?null:p.to+u):f.to=i}}if(s)for(var h=0;h<s.length;++h){var m=s[h];null!=m.to&&(m.to+=u),null==m.from?It(l,m.marker)||(m.from=u,c&&(l||(l=[])).push(m)):(m.from+=u,c&&(l||(l=[])).push(m))}l&&(l=Rt(l)),s&&s!=l&&(s=Rt(s));var g=[l];if(!c){var v,y=t.text.length-2;if(y>0&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new Et(l[b].marker,null,null));for(var w=0;w<y;++w)g.push(v);g.push(s)}return g}function Rt(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Bt(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=R(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],l=a.find(0),s=0;s<i.length;++s){var c=i[s];if(!(lt(c.to,l.from)<0||lt(c.from,l.to)>0)){var u=[s,1],d=lt(c.from,l.from),f=lt(c.to,l.to);(d<0||!a.inclusiveLeft&&!d)&&u.push({from:c.from,to:l.from}),(f>0||!a.inclusiveRight&&!f)&&u.push({from:l.to,to:c.to}),i.splice.apply(i,u),s+=u.length-3}}return i}function jt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function Ut(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Kt(e){return e.inclusiveLeft?-1:0}function Vt(e){return e.inclusiveRight?1:0}function $t(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=lt(n.from,i.from)||Kt(e)-Kt(t);if(o)return-o;var a=lt(n.to,i.to)||Vt(e)-Vt(t);return a||t.id-e.id}function Gt(e,t){var r,n=Ot&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||$t(r,i.marker)<0)&&(r=i.marker);return r}function Xt(e){return Gt(e,!0)}function Yt(e){return Gt(e,!1)}function Zt(e,t){var r,n=Ot&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||$t(r,o.marker)<0)&&(r=o.marker)}return r}function Qt(e,t,r,n,i){var o=Qe(e,t),a=Ot&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var c=s.marker.find(0),u=lt(c.from,r)||Kt(s.marker)-Kt(i),d=lt(c.to,n)||Vt(s.marker)-Vt(i);if(!(u>=0&&d<=0||u<=0&&d>=0)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?lt(c.to,r)>=0:lt(c.to,r)>0)||u>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?lt(c.from,n)<=0:lt(c.from,n)<0)))return!0}}}function Jt(e){for(var t;t=Xt(e);)e=t.find(-1,!0).line;return e}function er(e){for(var t;t=Yt(e);)e=t.find(1,!0).line;return e}function tr(e){for(var t,r;t=Yt(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function rr(e,t){var r=Qe(e,t),n=Jt(r);return r==n?t:rt(n)}function nr(e,t){if(t>e.lastLine())return t;var r,n=Qe(e,t);if(!ir(e,n))return t;for(;r=Yt(n);)n=r.find(1,!0).line;return rt(n)+1}function ir(e,t){var r=Ot&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&or(e,t,n))return!0}}function or(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return or(e,n.line,It(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&or(e,t,i))return!0}function ar(e){for(var t=0,r=(e=Jt(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==r)break;t+=l.height}return t}function lr(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=Xt(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=Yt(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function sr(e){var t=e.display,r=e.doc;t.maxLine=Qe(r,r.first),t.maxLineLength=lr(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=lr(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}var cr=function(e,t,r){this.text=e,Ut(this,t),this.height=r?r(this):1};function ur(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),jt(e),Ut(e,r);var i=n?n(e):1;i!=e.height&&tt(e,i)}function dr(e){e.parent=null,jt(e)}cr.prototype.lineNo=function(){return rt(this)},ke(cr);var fr={},pr={};function hr(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?pr:fr;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function mr(e,t){var r=O("span",null,null,s?"padding-right: .1px":null),n={pre:O("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;n.pos=0,n.addToken=vr,De(e.display.measure)&&(a=pe(o,e.doc.direction))&&(n.addToken=br(n.addToken,a)),n.map=[],xr(o,n,bt(e,o,t!=e.display.externalMeasured&&rt(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=I(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=I(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(ze(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var l=n.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return ye(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=I(n.pre.className,n.textClass||"")),n}function gr(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function vr(e,t,r,n,i,o,s){if(t){var c,u=e.splitSpaces?yr(t,e.trailingSpace):t,d=e.cm.state.specialChars,f=!1;if(d.test(t)){c=document.createDocumentFragment();for(var p=0;;){d.lastIndex=p;var h=d.exec(t),m=h?h.index-p:t.length-p;if(m){var g=document.createTextNode(u.slice(p,p+m));a&&l<9?c.appendChild(A("span",[g])):c.appendChild(g),e.map.push(e.pos,e.pos+m,g),e.col+=m,e.pos+=m}if(!h)break;p+=m+1;var v=void 0;if("\t"==h[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=c.appendChild(A("span",X(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==h[0]||"\n"==h[0]?((v=c.appendChild(A("span","\r"==h[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",h[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(h[0])).setAttribute("cm-text",h[0]),a&&l<9?c.appendChild(A("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),a&&l<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),r||n||i||f||o||s){var w=r||"";n&&(w+=n),i&&(w+=i);var x=A("span",[c],w,o);if(s)for(var k in s)s.hasOwnProperty(k)&&"style"!=k&&"class"!=k&&x.setAttribute(k,s[k]);return e.content.appendChild(x)}e.content.appendChild(c)}}function yr(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function br(e,t){return function(r,n,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var c=r.pos,u=c+n.length;;){for(var d=void 0,f=0;f<t.length&&!((d=t[f]).to>c&&d.from<=c);f++);if(d.to>=u)return e(r,n,i,o,a,l,s);e(r,n.slice(0,d.to-c),i,o,null,l,s),o=null,n=n.slice(d.to-c),c=d.to}}}function wr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function xr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,l,s,c,u,d,f,p=i.length,h=0,m=1,g="",v=0;;){if(v==h){s=c=u=l="",f=null,d=null,v=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],k=x.marker;if("bookmark"==k.type&&x.from==h&&k.widgetNode)y.push(k);else if(x.from<=h&&(null==x.to||x.to>h||k.collapsed&&x.to==h&&x.from==h)){if(null!=x.to&&x.to!=h&&v>x.to&&(v=x.to,c=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&x.from==h&&(u+=" "+k.startStyle),k.endStyle&&x.to==v&&(b||(b=[])).push(k.endStyle,x.to),k.title&&((f||(f={})).title=k.title),k.attributes)for(var _ in k.attributes)(f||(f={}))[_]=k.attributes[_];k.collapsed&&(!d||$t(d.marker,k)<0)&&(d=x)}else x.from>h&&v>x.from&&(v=x.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(c+=" "+b[C]);if(!d||d.from==h)for(var S=0;S<y.length;++S)wr(t,0,y[S]);if(d&&(d.from||0)==h){if(wr(t,(null==d.to?p+1:d.to)-h,d.marker,null==d.from),null==d.to)return;d.to==h&&(d=!1)}}if(h>=p)break;for(var M=Math.min(p,v);;){if(g){var T=h+g.length;if(!d){var L=T>M?g.slice(0,M-h):g;t.addToken(t,L,a?a+s:s,u,h+L.length==v?c:"",l,f)}if(T>=M){g=g.slice(M-h),h=M;break}h=T,u=""}g=i.slice(o,o=r[m++]),a=hr(r[m++],t.cm.options)}}else for(var N=1;N<r.length;N+=2)t.addToken(t,i.slice(o,o=r[N]),hr(r[N+1],t.cm.options))}function kr(e,t,r){this.line=t,this.rest=tr(t),this.size=this.rest?rt(Y(this.rest))-r+1:1,this.node=this.text=null,this.hidden=ir(e,t)}function _r(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new kr(e.doc,Qe(e.doc,o),o);n=o+a.size,i.push(a)}return i}var Cr=null;function Sr(e){Cr?Cr.ops.push(e):e.ownsGroup=Cr={ops:[e],delayedCallbacks:[]}}function Mr(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function Tr(e,t){var r=e.ownsGroup;if(r)try{Mr(r)}finally{Cr=null,t(r)}}var Lr=null;function Nr(e,t){var r=ge(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);Cr?n=Cr.delayedCallbacks:Lr?n=Lr:(n=Lr=[],setTimeout(Ar,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},a=0;a<r.length;++a)o(a)}}function Ar(){var e=Lr;Lr=null;for(var t=0;t<e.length;++t)e[t]()}function Or(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?Ir(e,t):"gutter"==o?Wr(e,t,r,n):"class"==o?Pr(e,t):"widget"==o&&qr(e,t,n)}t.changes=null}function zr(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&l<8&&(e.node.style.zIndex=2)),e.node}function Dr(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=zr(t);t.background=n.insertBefore(A("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}function Er(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):mr(e,t)}function Ir(e,t){var r=t.text.className,n=Er(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Pr(e,t)):r&&(t.text.className=r)}function Pr(e,t){Dr(e,t),t.line.wrapClass?zr(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Wr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=zr(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=zr(t),l=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(A("div",ot(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var c=e.display.gutterSpecs[s].className,u=o.hasOwnProperty(c)&&o[c];u&&l.appendChild(A("div",[u],"CodeMirror-gutter-elt","left: "+n.gutterLeft[c]+"px; width: "+n.gutterWidth[c]+"px"))}}}function qr(e,t,r){t.alignable&&(t.alignable=null);for(var n=S("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,n.test(i.className)&&t.node.removeChild(i);Hr(e,t,r)}function Fr(e,t,r,n){var i=Er(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Pr(e,t),Wr(e,t,r,n),Hr(e,t,n),t.node}function Hr(e,t,r){if(Rr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)Rr(e,t.rest[n],t,r,!1)}function Rr(e,t,r,n,i){if(t.widgets)for(var o=zr(r),a=0,l=t.widgets;a<l.length;++a){var s=l[a],c=A("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Br(s,c,r,n),e.display.input.setUneditable(c),i&&s.above?o.insertBefore(c,r.gutter||r.text):o.appendChild(c),Nr(s,"redraw")}}function Br(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function jr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!z(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,A("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function Ur(e,t){for(var r=Te(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Kr(e){return e.lineSpace.offsetTop}function Vr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function $r(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,A("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Gr(e){return B-e.display.nativeBarWidth}function Xr(e){return e.display.scroller.clientWidth-Gr(e)-e.display.barWidth}function Yr(e){return e.display.scroller.clientHeight-Gr(e)-e.display.barHeight}function Zr(e,t,r){var n=e.options.lineWrapping,i=n&&Xr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],c=a[l+1];Math.abs(s.bottom-c.bottom)>2&&o.push((s.bottom+c.top)/2-r.top)}}o.push(r.bottom-r.top)}}function Qr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(rt(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function Jr(e,t){var r=rt(t=Jt(t)),n=e.display.externalMeasured=new kr(e.doc,t,r);n.lineN=r;var i=n.built=mr(e,n);return n.text=i.pre,N(e.display.lineMeasure,i.pre),n}function en(e,t,r,n){return nn(e,rn(e,t),r,n)}function tn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Wn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function rn(e,t){var r=rt(t),n=tn(e,r);n&&!n.text?n=null:n&&n.changes&&(Or(e,n,r,zn(e)),e.curOp.forceUpdate=!0),n||(n=Jr(e,t));var i=Qr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function nn(e,t,r,n,i){t.before&&(r=-1);var o,a=r+(n||"");return t.cache.hasOwnProperty(a)?o=t.cache[a]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Zr(e,t.view,t.rect),t.hasHeights=!0),(o=cn(e,t,r,n)).bogus||(t.cache[a]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var on,an={left:0,right:0,top:0,bottom:0};function ln(e,t,r){for(var n,i,o,a,l,s,c=0;c<e.length;c+=3)if(l=e[c],s=e[c+1],t<l?(i=0,o=1,a="left"):t<s?o=1+(i=t-l):(c==e.length-3||t==s&&e[c+3]>t)&&(i=(o=s-l)-1,t>=s&&(a="right")),null!=i){if(n=e[c+2],l==s&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)n=e[2+(c-=3)],a="left";if("right"==r&&i==s-l)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)n=e[(c+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function sn(e,t){var r=an;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function cn(e,t,r,n){var i,o=ln(t.map,r,n),s=o.node,c=o.start,u=o.end,d=o.collapse;if(3==s.nodeType){for(var f=0;f<4;f++){for(;c&&ae(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ae(t.line.text.charAt(o.coverStart+u));)++u;if((i=a&&l<9&&0==c&&u==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():sn(M(s,c,u).getClientRects(),n)).left||i.right||0==c)break;u=c,c-=1,d="right"}a&&l<11&&(i=un(e.display.measure,i))}else{var p;c>0&&(d=n="right"),i=e.options.lineWrapping&&(p=s.getClientRects()).length>1?p["right"==n?p.length-1:0]:s.getBoundingClientRect()}if(a&&l<9&&!c&&(!i||!i.left&&!i.right)){var h=s.parentNode.getClientRects()[0];i=h?{left:h.left,right:h.left+On(e.display),top:h.top,bottom:h.bottom}:an}for(var m=i.top-t.rect.top,g=i.bottom-t.rect.top,v=(m+g)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],k={left:("right"==d?i.right:i.left)-t.rect.left,right:("left"==d?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=m,k.rbottom=g),k}function un(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Fe(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function dn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function fn(e){e.display.externalMeasure=null,L(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)dn(e.display.view[t])}function pn(e){fn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function hn(){return u&&v?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function mn(){return u&&v?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function gn(e){var t=Jt(e).widgets,r=0;if(t)for(var n=0;n<t.length;++n)t[n].above&&(r+=jr(t[n]));return r}function vn(e,t,r,n,i){if(!i){var o=gn(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var a=ar(t);if("local"==n?a+=Kr(e.display):a-=e.display.viewOffset,"page"==n||"window"==n){var l=e.display.lineSpace.getBoundingClientRect();a+=l.top+("window"==n?0:mn());var s=l.left+("window"==n?0:hn());r.left+=s,r.right+=s}return r.top+=a,r.bottom+=a,r}function yn(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=hn(),i-=mn();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function bn(e,t,r,n,i){return n||(n=Qe(e.doc,t.line)),vn(e,n,en(e,n,t.ch,i),r)}function wn(e,t,r,n,i,o){function a(t,a){var l=nn(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,vn(e,n,l,r)}n=n||Qe(e.doc,t.line),i||(i=rn(e,n));var l=pe(n,e.doc.direction),s=t.ch,c=t.sticky;if(s>=n.text.length?(s=n.text.length,c="before"):s<=0&&(s=0,c="after"),!l)return a("before"==c?s-1:s,"before"==c);function u(e,t,r){return a(r?e-1:e,1==l[t].level!=r)}var d=de(l,s,c),f=ue,p=u(s,d,"before"==c);return null!=f&&(p.other=u(s,f,"before"!=c)),p}function xn(e,t){var r=0;t=pt(e.doc,t),e.options.lineWrapping||(r=On(e.display)*t.ch);var n=Qe(e.doc,t.line),i=ar(n)+Kr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function kn(e,t,r,n,i){var o=at(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function _n(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return kn(n.first,0,null,-1,-1);var i=nt(n,r),o=n.first+n.size-1;if(i>o)return kn(n.first+n.size-1,Qe(n,o).text.length,null,1,1);t<0&&(t=0);for(var a=Qe(n,i);;){var l=Tn(e,a,i,t,r),s=Zt(a,l.ch+(l.xRel>0||l.outside>0?1:0));if(!s)return l;var c=s.find(1);if(c.line==i)return c;a=Qe(n,i=c.line)}}function Cn(e,t,r,n){n-=gn(t);var i=t.text.length,o=se((function(t){return nn(e,r,t-1).bottom<=n}),i,0);return{begin:o,end:i=se((function(t){return nn(e,r,t).top>n}),o,i)}}function Sn(e,t,r,n){return r||(r=rn(e,t)),Cn(e,t,r,vn(e,t,nn(e,r,n),"line").top)}function Mn(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function Tn(e,t,r,n,i){i-=ar(t);var o=rn(e,t),a=gn(t),l=0,s=t.text.length,c=!0,u=pe(t,e.doc.direction);if(u){var d=(e.options.lineWrapping?Nn:Ln)(e,t,r,o,u,n,i);l=(c=1!=d.level)?d.from:d.to-1,s=c?d.to:d.from-1}var f,p,h=null,m=null,g=se((function(t){var r=nn(e,o,t);return r.top+=a,r.bottom+=a,!!Mn(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(h=t,m=r),!0)}),l,s),v=!1;if(m){var y=n-m.left<m.right-n,b=y==c;g=h+(b?0:1),p=b?"after":"before",f=y?m.left:m.right}else{c||g!=s&&g!=l||g++,p=0==g?"after":g==t.text.length?"before":nn(e,o,g-(c?1:0)).bottom+a<=i==c?"after":"before";var w=wn(e,at(r,g,p),"line",t,o);f=w.left,v=i<w.top?-1:i>=w.bottom?1:0}return kn(r,g=le(t.text,g,1),p,v,n-f)}function Ln(e,t,r,n,i,o,a){var l=se((function(l){var s=i[l],c=1!=s.level;return Mn(wn(e,at(r,c?s.to:s.from,c?"before":"after"),"line",t,n),o,a,!0)}),0,i.length-1),s=i[l];if(l>0){var c=1!=s.level,u=wn(e,at(r,c?s.from:s.to,c?"after":"before"),"line",t,n);Mn(u,o,a,!0)&&u.top>a&&(s=i[l-1])}return s}function Nn(e,t,r,n,i,o,a){var l=Cn(e,t,n,a),s=l.begin,c=l.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,d=null,f=0;f<i.length;f++){var p=i[f];if(!(p.from>=c||p.to<=s)){var h=nn(e,n,1!=p.level?Math.min(c,p.to)-1:Math.max(s,p.from)).right,m=h<o?o-h+1e9:h-o;(!u||d>m)&&(u=p,d=m)}}return u||(u=i[i.length-1]),u.from<s&&(u={from:s,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}function An(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==on){on=A("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)on.appendChild(document.createTextNode("x")),on.appendChild(A("br"));on.appendChild(document.createTextNode("x"))}N(e.measure,on);var r=on.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),L(e.measure),r||1}function On(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),r=A("pre",[t],"CodeMirror-line-like");N(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function zn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var l=e.display.gutterSpecs[a].className;r[l]=o.offsetLeft+o.clientLeft+i,n[l]=o.clientWidth}return{fixedPos:Dn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function Dn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function En(e){var t=An(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/On(e.display)-3);return function(i){if(ir(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function In(e){var t=e.doc,r=En(e);t.iter((function(e){var t=r(e);t!=e.height&&tt(e,t)}))}function Pn(e,t,r,n){var i=e.display;if(!r&&"true"==Te(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(e){return null}var s,c=_n(e,o,a);if(n&&c.xRel>0&&(s=Qe(e.doc,c.line).text).length==c.ch){var u=F(s,s.length,e.options.tabSize)-s.length;c=at(c.line,Math.max(0,Math.round((o-$r(e.display).left)/On(e.display))-u))}return c}function Wn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function qn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Ot&&rr(e.doc,t)<i.viewTo&&Hn(e);else if(r<=i.viewFrom)Ot&&nr(e.doc,r+n)>i.viewFrom?Hn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Hn(e);else if(t<=i.viewFrom){var o=Rn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Hn(e)}else if(r>=i.viewTo){var a=Rn(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):Hn(e)}else{var l=Rn(e,t,t,-1),s=Rn(e,r,r+n,1);l&&s?(i.view=i.view.slice(0,l.index).concat(_r(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=n):Hn(e)}var c=i.externalMeasured;c&&(r<c.lineN?c.lineN+=n:t<c.lineN+c.size&&(i.externalMeasured=null))}function Fn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Wn(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==R(a,r)&&a.push(r)}}}function Hn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Rn(e,t,r,n){var i,o=Wn(e,t),a=e.display.view;if(!Ot||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(n>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,r+=i}for(;rr(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Bn(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=_r(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=_r(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Wn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(_r(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Wn(e,r)))),n.viewTo=r}function jn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function Un(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Kn(e,t){void 0===t&&(t=!0);var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=e.options.$customCursor;a&&(t=!0);for(var l=0;l<r.sel.ranges.length;l++)if(t||l!=r.sel.primIndex){var s=r.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var c=s.empty();if(a){var u=a(e,s);u&&Vn(e,u,i)}else(c||e.options.showCursorWhenSelecting)&&Vn(e,s.head,i);c||Gn(e,s,o)}}return n}function Vn(e,t,r){var n=wn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=bn(e,t,"div",null,null),a=o.right-o.left;i.style.width=(a>0?a:e.defaultCharWidth())+"px"}if(n.other){var l=r.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=n.other.left+"px",l.style.top=n.other.top+"px",l.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function $n(e,t){return e.top-t.top||e.left-t.left}function Gn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),a=$r(e.display),l=a.left,s=Math.max(n.sizerWidth,Xr(e)-n.sizer.offsetLeft)-a.right,c="ltr"==i.direction;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?s-e:r)+"px;\n                             height: "+(n-t)+"px"))}function d(t,r,n){var o,a,d=Qe(i,t),f=d.text.length;function p(r,n){return bn(e,at(t,r),"div",d,n)}function h(t,r,n){var i=Sn(e,d,null,t),o="ltr"==r==("after"==n)?"left":"right";return p("after"==n?i.begin:i.end-(/\s/.test(d.text.charAt(i.end-1))?2:1),o)[o]}var m=pe(d,i.direction);return ce(m,r||0,null==n?f:n,(function(e,t,i,d){var g="ltr"==i,v=p(e,g?"left":"right"),y=p(t-1,g?"right":"left"),b=null==r&&0==e,w=null==n&&t==f,x=0==d,k=!m||d==m.length-1;if(y.top-v.top<=3){var _=(c?w:b)&&k,C=(c?b:w)&&x?l:(g?v:y).left,S=_?s:(g?y:v).right;u(C,v.top,S-C,v.bottom)}else{var M,T,L,N;g?(M=c&&b&&x?l:v.left,T=c?s:h(e,i,"before"),L=c?l:h(t,i,"after"),N=c&&w&&k?s:y.right):(M=c?h(e,i,"before"):l,T=!c&&b&&x?s:v.right,L=!c&&w&&k?l:y.left,N=c?h(t,i,"after"):s),u(M,v.top,T-M,v.bottom),v.bottom<y.top&&u(l,v.bottom,null,y.top),u(L,y.top,N-L,y.bottom)}(!o||$n(v,o)<0)&&(o=v),$n(y,o)<0&&(o=y),(!a||$n(v,a)<0)&&(a=v),$n(y,a)<0&&(a=y)})),{start:o,end:a}}var f=t.from(),p=t.to();if(f.line==p.line)d(f.line,f.ch,p.ch);else{var h=Qe(i,f.line),m=Qe(i,p.line),g=Jt(h)==Jt(m),v=d(f.line,f.ch,g?h.text.length+1:null).end,y=d(p.line,g?0:null,p.ch).start;g&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(l,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(l,v.bottom,null,y.top)}r.appendChild(o)}function Xn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||Jn(e),t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Yn(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||Qn(e))}function Zn(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Jn(e))}),100)}function Qn(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(ye(e,"focus",e,t),e.state.focused=!0,E(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Xn(e))}function Jn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ye(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function ei(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,s=0;s<t.view.length;s++){var c=t.view[s],u=e.options.lineWrapping,d=void 0,f=0;if(!c.hidden){if(i+=c.line.height,a&&l<8){var p=c.node.offsetTop+c.node.offsetHeight;d=p-r,r=p}else{var h=c.node.getBoundingClientRect();d=h.bottom-h.top,!u&&c.text.firstChild&&(f=c.text.firstChild.getBoundingClientRect().right-h.left-1)}var m=c.line.height-d;if((m>.005||m<-.005)&&(i<n&&(o-=m),tt(c.line,d),ti(c.line),c.rest))for(var g=0;g<c.rest.length;g++)ti(c.rest[g]);if(f>e.display.sizerWidth){var v=Math.ceil(f/On(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=c.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function ti(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function ri(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Kr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=nt(t,n),a=nt(t,i);if(r&&r.ensure){var l=r.ensure.from.line,s=r.ensure.to.line;l<o?(o=l,a=nt(t,ar(Qe(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=nt(t,ar(Qe(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function ni(e,t){if(!be(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!m){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Kr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Gr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}function ii(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==t.sticky?at(t.line,t.ch+1,"before"):t,t=t.ch?at(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var a=!1,l=wn(e,t),s=r&&r!=t?wn(e,r):l,c=ai(e,i={left:Math.min(l.left,s.left),top:Math.min(l.top,s.top)-n,right:Math.max(l.left,s.left),bottom:Math.max(l.bottom,s.bottom)+n}),u=e.doc.scrollTop,d=e.doc.scrollLeft;if(null!=c.scrollTop&&(pi(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=c.scrollLeft&&(mi(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-d)>1&&(a=!0)),!a)break}return i}function oi(e,t){var r=ai(e,t);null!=r.scrollTop&&pi(e,r.scrollTop),null!=r.scrollLeft&&mi(e,r.scrollLeft)}function ai(e,t){var r=e.display,n=An(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=Yr(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var l=e.doc.height+Vr(r),s=t.top<n,c=t.bottom>l-n;if(t.top<i)a.scrollTop=s?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?l:t.bottom)-o);u!=i&&(a.scrollTop=u)}var d=e.options.fixedGutter?0:r.gutters.offsetWidth,f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft-d,p=Xr(e)-r.gutters.offsetWidth,h=t.right-t.left>p;return h&&(t.right=t.left+p),t.left<10?a.scrollLeft=0:t.left<f?a.scrollLeft=Math.max(0,t.left+d-(h?0:10)):t.right>p+f-3&&(a.scrollLeft=t.right+(h?0:10)-p),a}function li(e,t){null!=t&&(di(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function si(e){di(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function ci(e,t,r){null==t&&null==r||di(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function ui(e,t){di(e),e.curOp.scrollToPos=t}function di(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,fi(e,xn(e,t.from),xn(e,t.to),t.margin))}function fi(e,t,r,n){var i=ai(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});ci(e,i.scrollLeft,i.scrollTop)}function pi(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||Ui(e,{top:t}),hi(e,t,!0),r&&Ui(e),Pi(e,100))}function hi(e,t,r){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function mi(e,t,r,n){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,Gi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function gi(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+Vr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Gr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var vi=function(e,t,r){this.cm=r;var n=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),me(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),me(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,a&&l<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};vi.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},vi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},vi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},vi.prototype.zeroWidthHack=function(){var e=b&&!h?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new H,this.disableVert=new H},vi.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.pointerEvents="none":t.set(1e3,n)}e.style.pointerEvents="auto",t.set(1e3,n)},vi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var yi=function(){};function bi(e,t){t||(t=gi(e));var r=e.display.barWidth,n=e.display.barHeight;wi(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&ei(e),wi(e,gi(e)),r=e.display.barWidth,n=e.display.barHeight}function wi(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}yi.prototype.update=function(){return{bottom:0,right:0}},yi.prototype.setScrollLeft=function(){},yi.prototype.setScrollTop=function(){},yi.prototype.clear=function(){};var xi={native:vi,null:yi};function ki(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new xi[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),me(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?mi(e,t):pi(e,t)}),e),e.display.scrollbars.addClass&&E(e.display.wrapper,e.display.scrollbars.addClass)}var _i=0;function Ci(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++_i,markArrays:null},Sr(e.curOp)}function Si(e){var t=e.curOp;t&&Tr(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;Mi(e)}))}function Mi(e){for(var t=e.ops,r=0;r<t.length;r++)Ti(t[r]);for(var n=0;n<t.length;n++)Li(t[n]);for(var i=0;i<t.length;i++)Ni(t[i]);for(var o=0;o<t.length;o++)Ai(t[o]);for(var a=0;a<t.length;a++)Oi(t[a])}function Ti(e){var t=e.cm,r=t.display;Fi(t),e.updateMaxLine&&sr(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new qi(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Li(e){e.updatedDisplay=e.mustUpdate&&Bi(e.cm,e.update)}function Ni(e){var t=e.cm,r=t.display;e.updatedDisplay&&ei(t),e.barMeasure=gi(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=en(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Gr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Xr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Ai(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&mi(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==D();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&bi(t,e.barMeasure),e.updatedDisplay&&$i(t,e.barMeasure),e.selectionChanged&&Xn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&Yn(e.cm)}function Oi(e){var t=e.cm,r=t.display,n=t.doc;e.updatedDisplay&&ji(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&hi(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&mi(t,e.scrollLeft,!0,!0),e.scrollToPos&&ni(t,ii(t,pt(n,e.scrollToPos.from),pt(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||ye(i[a],"hide");if(o)for(var l=0;l<o.length;++l)o[l].lines.length&&ye(o[l],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&ye(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function zi(e,t){if(e.curOp)return t();Ci(e);try{return t()}finally{Si(e)}}function Di(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Ci(e);try{return t.apply(e,arguments)}finally{Si(e)}}}function Ei(e){return function(){if(this.curOp)return e.apply(this,arguments);Ci(this);try{return e.apply(this,arguments)}finally{Si(this)}}}function Ii(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Ci(t);try{return e.apply(this,arguments)}finally{Si(t)}}}function Pi(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,W(Wi,e))}function Wi(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=wt(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(n.line>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength?Ge(t.mode,n.state):null,s=yt(e,o,n,!0);l&&(n.state=l),o.styles=s.styles;var c=o.styleClasses,u=s.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var d=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),f=0;!d&&f<a.length;++f)d=a[f]!=o.styles[f];d&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&xt(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return Pi(e,e.options.workDelay),!0})),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&zi(e,(function(){for(var t=0;t<i.length;t++)Fn(e,i[t],"text")}))}}var qi=function(e,t,r){var n=e.display;this.viewport=t,this.visible=ri(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Xr(e),this.force=r,this.dims=zn(e),this.events=[]};function Fi(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Gr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Gr(e)+"px",t.scrollbarsClipped=!0)}function Hi(e){if(e.hasFocus())return null;var t=D();if(!t||!z(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&z(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function Ri(e){if(e&&e.activeElt&&e.activeElt!=D()&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&z(document.body,e.anchorNode)&&z(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}function Bi(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Hn(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==jn(e))return!1;Xi(e)&&(Hn(e),t.dims=zn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),Ot&&(o=rr(e.doc,o),a=nr(e.doc,a));var l=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;Bn(e,o,a),r.viewOffset=ar(Qe(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var s=jn(e);if(!l&&0==s&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var c=Hi(e);return s>4&&(r.lineDiv.style.display="none"),Ki(e,r.updateLineNumbers,t.dims),s>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,Ri(c),L(r.cursorDiv),L(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,l&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Pi(e,400)),r.updateLineNumbers=null,!0}function ji(e,t){for(var r=t.viewport,n=!0;;n=!1){if(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Xr(e))n&&(t.visible=ri(e.display,e.doc,r));else if(r&&null!=r.top&&(r={top:Math.min(e.doc.height+Vr(e.display)-Yr(e),r.top)}),t.visible=ri(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Bi(e,t))break;ei(e);var i=gi(e);Un(e),bi(e,i),$i(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Ui(e,t){var r=new qi(e,t);if(Bi(e,r)){ei(e),ji(e,r);var n=gi(e);Un(e),bi(e,n),$i(e,n),r.finish()}}function Ki(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function l(t){var r=t.nextSibling;return s&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var c=n.view,u=n.viewFrom,d=0;d<c.length;d++){var f=c[d];if(f.hidden);else if(f.node&&f.node.parentNode==o){for(;a!=f.node;)a=l(a);var p=i&&null!=t&&t<=u&&f.lineNumber;f.changes&&(R(f.changes,"gutter")>-1&&(p=!1),Or(e,f,u,r)),p&&(L(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(ot(e.options,u)))),a=f.node.nextSibling}else{var h=Fr(e,f,u,r);o.insertBefore(h,a)}u+=f.size}for(;a;)a=l(a)}function Vi(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Nr(e,"gutterChanged",e)}function $i(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Gr(e)+"px"}function Gi(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=Dn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var l=r[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function Xi(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=ot(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(A("div",[A("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",Vi(e.display),!0}return!1}function Yi(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:a})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function Zi(e){var t=e.gutters,r=e.gutterSpecs;L(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,a=i.style,l=t.appendChild(A("div",null,"CodeMirror-gutter "+o));a&&(l.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=l,l.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",Vi(e)}function Qi(e){Zi(e.display),qn(e),Gi(e)}function Ji(e,t,n,i){var o=this;this.input=n,o.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=O("div",null,"CodeMirror-code"),o.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=A("div",null,"CodeMirror-cursors"),o.measure=A("div",null,"CodeMirror-measure"),o.lineMeasure=A("div",null,"CodeMirror-measure"),o.lineSpace=O("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=O("div",[o.lineSpace],"CodeMirror-lines");o.mover=A("div",[c],null,"position: relative"),o.sizer=A("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=A("div",null,null,"position: absolute; height: "+B+"px; width: 1px;"),o.gutters=A("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=A("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=A("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),o.wrapper.setAttribute("translate","no"),a&&l<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),s||r&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=Yi(i.gutters,i.lineNumbers),Zi(o),n.init(o)}qi.prototype.signal=function(e,t){xe(e,t)&&this.events.push(arguments)},qi.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ye.apply(null,this.events[e])};var eo=0,to=null;function ro(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function no(e){var t=ro(e);return t.x*=to,t.y*=to,t}function io(e,t){u&&d>=102&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout((function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""}),100));var n=ro(t),i=n.x,o=n.y,a=to;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,a=1);var l=e.display,c=l.scroller,p=c.scrollWidth>c.clientWidth,h=c.scrollHeight>c.clientHeight;if(i&&p||o&&h){if(o&&b&&s)e:for(var m=t.target,g=l.view;m!=c;m=m.parentNode)for(var v=0;v<g.length;v++)if(g[v].node==m){e.display.currentWheelTarget=m;break e}if(i&&!r&&!f&&null!=a)return o&&h&&pi(e,Math.max(0,c.scrollTop+o*a)),mi(e,Math.max(0,c.scrollLeft+i*a)),(!o||o&&h)&&_e(t),void(l.wheelStartX=null);if(o&&null!=a){var y=o*a,w=e.doc.scrollTop,x=w+l.wrapper.clientHeight;y<0?w=Math.max(0,w+y-50):x=Math.min(e.doc.height,x+y+50),Ui(e,{top:w,bottom:x})}eo<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=c.scrollLeft,l.wheelStartY=c.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var e=c.scrollLeft-l.wheelStartX,t=c.scrollTop-l.wheelStartY,r=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,r&&(to=(to*eo+r)/(eo+1),++eo)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}a?to=-.53:r?to=15:u?to=-.7:p&&(to=-1/3);var oo=function(e,t){this.ranges=e,this.primIndex=t};oo.prototype.primary=function(){return this.ranges[this.primIndex]},oo.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!st(r.anchor,n.anchor)||!st(r.head,n.head))return!1}return!0},oo.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new ao(ct(this.ranges[t].anchor),ct(this.ranges[t].head));return new oo(e,this.primIndex)},oo.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},oo.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(lt(t,n.from())>=0&&lt(e,n.to())<=0)return r}return-1};var ao=function(e,t){this.anchor=e,this.head=t};function lo(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort((function(e,t){return lt(e.from(),t.from())})),r=R(t,i);for(var o=1;o<t.length;o++){var a=t[o],l=t[o-1],s=lt(l.to(),a.from());if(n&&!a.empty()?s>0:s>=0){var c=dt(l.from(),a.from()),u=ut(l.to(),a.to()),d=l.empty()?a.from()==a.head:l.from()==l.head;o<=r&&--r,t.splice(--o,2,new ao(d?u:c,d?c:u))}}return new oo(t,r)}function so(e,t){return new oo([new ao(e,t||e)],0)}function co(e){return e.text?at(e.from.line+e.text.length-1,Y(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function uo(e,t){if(lt(e,t.from)<0)return e;if(lt(e,t.to)<=0)return co(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=co(t).ch-t.to.ch),at(r,n)}function fo(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new ao(uo(i.anchor,t),uo(i.head,t)))}return lo(e.cm,r,e.sel.primIndex)}function po(e,t,r){return e.line==t.line?at(r.line,e.ch-t.ch+r.ch):at(r.line+(e.line-t.line),e.ch)}function ho(e,t,r){for(var n=[],i=at(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=po(l.from,i,o),c=po(co(l),i,o);if(i=l.to,o=c,"around"==r){var u=e.sel.ranges[a],d=lt(u.head,u.anchor)<0;n[a]=new ao(d?c:s,d?s:c)}else n[a]=new ao(s,s)}return new oo(n,e.sel.primIndex)}function mo(e){e.doc.mode=Ke(e.options,e.doc.modeOption),go(e)}function go(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Pi(e,100),e.state.modeGen++,e.curOp&&qn(e)}function vo(e,t){return 0==t.from.ch&&0==t.to.ch&&""==Y(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function yo(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){ur(e,r,i,n),Nr(e,"change",e,t)}function a(e,t){for(var r=[],o=e;o<t;++o)r.push(new cr(c[o],i(o),n));return r}var l=t.from,s=t.to,c=t.text,u=Qe(e,l.line),d=Qe(e,s.line),f=Y(c),p=i(c.length-1),h=s.line-l.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(vo(e,t)){var m=a(0,c.length-1);o(d,d.text,p),h&&e.remove(l.line,h),m.length&&e.insert(l.line,m)}else if(u==d)if(1==c.length)o(u,u.text.slice(0,l.ch)+f+u.text.slice(s.ch),p);else{var g=a(1,c.length-1);g.push(new cr(f+u.text.slice(s.ch),p,n)),o(u,u.text.slice(0,l.ch)+c[0],i(0)),e.insert(l.line+1,g)}else if(1==c.length)o(u,u.text.slice(0,l.ch)+c[0]+d.text.slice(s.ch),i(0)),e.remove(l.line+1,h);else{o(u,u.text.slice(0,l.ch)+c[0],i(0)),o(d,f+d.text.slice(s.ch),p);var v=a(1,c.length-1);h>1&&e.remove(l.line+1,h-1),e.insert(l.line+1,v)}Nr(e,"change",e,t)}function bo(e,t,r){function n(e,i,o){if(e.linked)for(var a=0;a<e.linked.length;++a){var l=e.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;r&&!s||(t(l.doc,s),n(l.doc,e,s))}}}n(e,null,!0)}function wo(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,In(e),mo(e),xo(e),e.options.direction=t.direction,e.options.lineWrapping||sr(e),e.options.mode=t.modeOption,qn(e)}function xo(e){("rtl"==e.doc.direction?E:T)(e.display.lineDiv,"CodeMirror-rtl")}function ko(e){zi(e,(function(){xo(e),qn(e)}))}function _o(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Co(e,t){var r={from:ct(t.from),to:co(t),text:Je(e,t.from,t.to)};return Oo(e,r,t.from.line,t.to.line+1),bo(e,(function(e){return Oo(e,r,t.from.line,t.to.line+1)}),!0),r}function So(e){for(;e.length&&Y(e).ranges;)e.pop()}function Mo(e,t){return t?(So(e.done),Y(e.done)):e.done.length&&!Y(e.done).ranges?Y(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),Y(e.done)):void 0}function To(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>l-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=Mo(i,i.lastOp==n)))a=Y(o.changes),0==lt(t.from,t.to)&&0==lt(t.from,a.to)?a.to=co(t):o.changes.push(Co(e,t));else{var s=Y(i.done);for(s&&s.ranges||Ao(e.sel,i.done),o={changes:[Co(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||ye(e,"historyAdded")}function Lo(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function No(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||Lo(e,o,Y(i.done),t))?i.done[i.done.length-1]=t:Ao(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&So(i.undone)}function Ao(e,t){var r=Y(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Oo(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function zo(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Do(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(zo(r[i]));return n}function Eo(e,t){var r=Do(e,t),n=Ht(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else a&&(r[i]=a)}return r}function Io(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?oo.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];n.push({changes:l});for(var s=0;s<a.length;++s){var c=a[s],u=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),t)for(var d in c)(u=d.match(/^spans_(\d+)$/))&&R(t,Number(u[1]))>-1&&(Y(l)[d]=c[d],delete c[d])}}}return n}function Po(e,t,r,n){if(n){var i=e.anchor;if(r){var o=lt(t,i)<0;o!=lt(r,i)<0?(i=t,t=r):o!=lt(t,r)<0&&(t=r)}return new ao(i,t)}return new ao(r||t,t)}function Wo(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),jo(e,new oo([Po(e.sel.primary(),t,r,i)],0),n)}function qo(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Po(e.sel.ranges[o],t[o],null,i);jo(e,lo(e.cm,n,e.sel.primIndex),r)}function Fo(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,jo(e,lo(e.cm,i,e.sel.primIndex),n)}function Ho(e,t,r,n){jo(e,so(t,r),n)}function Ro(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new ao(pt(e,t[r].anchor),pt(e,t[r].head))},origin:r&&r.origin};return ye(e,"beforeSelectionChange",e,n),e.cm&&ye(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?lo(e.cm,n.ranges,n.ranges.length-1):t}function Bo(e,t,r){var n=e.history.done,i=Y(n);i&&i.ranges?(n[n.length-1]=t,Uo(e,t,r)):jo(e,t,r)}function jo(e,t,r){Uo(e,t,r),No(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Uo(e,t,r){(xe(e,"beforeSelectionChange")||e.cm&&xe(e.cm,"beforeSelectionChange"))&&(t=Ro(e,t,r));var n=r&&r.bias||(lt(t.primary().head,e.sel.primary().head)<0?-1:1);Ko(e,$o(e,t,n,!0)),r&&!1===r.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||si(e.cm)}function Ko(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,we(e.cm)),Nr(e,"cursorActivity",e))}function Vo(e){Ko(e,$o(e,e.sel,null,!1))}function $o(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Xo(e,a.anchor,l&&l.anchor,r,n),c=Xo(e,a.head,l&&l.head,r,n);(i||s!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new ao(s,c))}return i?lo(e.cm,i,t.primIndex):t}function Go(e,t,r,n,i){var o=Qe(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker,c="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,u="selectRight"in s?!s.selectRight:s.inclusiveRight;if((null==l.from||(c?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(u?l.to>=t.ch:l.to>t.ch))){if(i&&(ye(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(r){var d=s.find(n<0?1:-1),f=void 0;if((n<0?u:c)&&(d=Yo(e,d,-n,d&&d.line==t.line?o:null)),d&&d.line==t.line&&(f=lt(d,r))&&(n<0?f<0:f>0))return Go(e,d,t,n,i)}var p=s.find(n<0?-1:1);return(n<0?c:u)&&(p=Yo(e,p,n,p.line==t.line?o:null)),p?Go(e,p,t,n,i):null}}return t}function Xo(e,t,r,n,i){var o=n||1,a=Go(e,t,r,o,i)||!i&&Go(e,t,r,o,!0)||Go(e,t,r,-o,i)||!i&&Go(e,t,r,-o,!0);return a||(e.cantEdit=!0,at(e.first,0))}function Yo(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?pt(e,at(t.line-1)):null:r>0&&t.ch==(n||Qe(e,t.line)).text.length?t.line<e.first+e.size-1?at(t.line+1,0):null:new at(t.line,t.ch+r)}function Zo(e){e.setSelection(at(e.firstLine(),0),at(e.lastLine()),U)}function Qo(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=pt(e,t)),r&&(n.to=pt(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),ye(e,"beforeChange",e,n),e.cm&&ye(e.cm,"beforeChange",e.cm,n),n.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function Jo(e,t,r){if(e.cm){if(!e.cm.curOp)return Di(e.cm,Jo)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(xe(e,"beforeChange")||e.cm&&xe(e.cm,"beforeChange"))||(t=Qo(e,t,!0))){var n=At&&!r&&Bt(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)ea(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else ea(e,t)}}function ea(e,t){if(1!=t.text.length||""!=t.text[0]||0!=lt(t.from,t.to)){var r=fo(e,t);To(e,t,r,e.cm?e.cm.curOp.id:NaN),na(e,t,r,Ht(e,t));var n=[];bo(e,(function(e,r){r||-1!=R(n,e.history)||(sa(e.history,t),n.push(e.history)),na(e,t,null,Ht(e,t))}))}}function ta(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,a=e.sel,l="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,c=0;c<l.length&&(i=l[c],r?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=l.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=l.pop()).ranges){if(n)return void l.push(i);break}if(Ao(i,s),r&&!i.equals(e.sel))return void jo(e,i,{clearRedo:!1});a=i}var u=[];Ao(a,s),s.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var d=xe(e,"beforeChange")||e.cm&&xe(e.cm,"beforeChange"),f=function(r){var n=i.changes[r];if(n.origin=t,d&&!Qo(e,n,!1))return l.length=0,{};u.push(Co(e,n));var o=r?fo(e,n):Y(l);na(e,n,o,Eo(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:co(n)});var a=[];bo(e,(function(e,t){t||-1!=R(a,e.history)||(sa(e.history,n),a.push(e.history)),na(e,n,null,Eo(e,n))}))},p=i.changes.length-1;p>=0;--p){var h=f(p);if(h)return h.v}}}}function ra(e,t){if(0!=t&&(e.first+=t,e.sel=new oo(Z(e.sel.ranges,(function(e){return new ao(at(e.anchor.line+t,e.anchor.ch),at(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){qn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Fn(e.cm,n,"gutter")}}function na(e,t,r,n){if(e.cm&&!e.cm.curOp)return Di(e.cm,na)(e,t,r,n);if(t.to.line<e.first)ra(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);ra(e,i),t={from:at(e.first,0),to:at(t.to.line+i,t.to.ch),text:[Y(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:at(o,Qe(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Je(e,t.from,t.to),r||(r=fo(e,t)),e.cm?ia(e.cm,t,n):yo(e,t,n),Uo(e,r,U),e.cantEdit&&Xo(e,at(e.firstLine(),0))&&(e.cantEdit=!1)}}function ia(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=rt(Jt(Qe(n,o.line))),n.iter(s,a.line+1,(function(e){if(e==i.maxLine)return l=!0,!0}))),n.sel.contains(t.from,t.to)>-1&&we(e),yo(n,t,r,En(e)),e.options.lineWrapping||(n.iter(s,o.line+t.text.length,(function(e){var t=lr(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)})),l&&(e.curOp.updateMaxLine=!0)),Nt(n,o.line),Pi(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?qn(e):o.line!=a.line||1!=t.text.length||vo(e.doc,t)?qn(e,o.line,a.line+1,c):Fn(e,o.line,"text");var u=xe(e,"changes"),d=xe(e,"change");if(d||u){var f={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&Nr(e,"change",e,f),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}function oa(e,t,r,n,i){var o;n||(n=r),lt(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),Jo(e,{from:r,to:n,text:t,origin:i})}function aa(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function la(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)aa(o.ranges[l].anchor,t,r,n),aa(o.ranges[l].head,t,r,n)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(r<c.from.line)c.from=at(c.from.line+n,c.from.ch),c.to=at(c.to.line+n,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function sa(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;la(e.done,r,n,i),la(e.undone,r,n,i)}function ca(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=Qe(e,ft(e,t)):i=rt(t),null==i?null:(n(o,i)&&e.cm&&Fn(e.cm,i,r),o)}function ua(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function da(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}ao.prototype.from=function(){return dt(this.anchor,this.head)},ao.prototype.to=function(){return ut(this.anchor,this.head)},ao.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},ua.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,dr(i),Nr(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},da.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),a=n.height;if(n.removeInner(e,o),this.height-=a-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof ua))){var l=[];this.collapse(l),this.children=[new ua(l)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,l=a;l<i.lines.length;){var s=new ua(i.lines.slice(l,l+=25));i.height-=s.height,this.children.splice(++n,0,s),s.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new da(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=R(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new da(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var fa=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function pa(e,t,r){ar(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&li(e,r)}function ha(e,t,r,n){var i=new fa(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),ca(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!ir(e,t)){var n=ar(t)<e.scrollTop;tt(t,t.height+jr(i)),n&&li(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&Nr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:rt(t)),i}fa.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=rt(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=jr(this);tt(r,Math.max(0,r.height-o)),e&&(zi(e,(function(){pa(e,r,-o),Fn(e,n,"widget")})),Nr(e,"lineWidgetCleared",e,this,n))}},fa.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=jr(this)-t;i&&(ir(this.doc,n)||tt(n,n.height+i),r&&zi(r,(function(){r.curOp.forceUpdate=!0,pa(r,n,i),Nr(r,"lineWidgetChanged",r,e,rt(n))})))},ke(fa);var ma=0,ga=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++ma};function va(e,t,r,n,i){if(n&&n.shared)return ba(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Di(e.cm,va)(e,t,r,n,i);var o=new ga(e,i),a=lt(t,r);if(n&&q(n,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=O("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Qt(e,t.line,t,r,o)||t.line!=r.line&&Qt(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Dt()}o.addToHistory&&To(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var l,s=t.line,c=e.cm;if(e.iter(s,r.line+1,(function(n){c&&o.collapsed&&!c.options.lineWrapping&&Jt(n)==c.display.maxLine&&(l=!0),o.collapsed&&s!=t.line&&tt(n,0),Wt(n,new Et(o,s==t.line?t.ch:null,s==r.line?r.ch:null),e.cm&&e.cm.curOp),++s})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){ir(e,t)&&tt(t,0)})),o.clearOnEnter&&me(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(zt(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++ma,o.atomic=!0),c){if(l&&(c.curOp.updateMaxLine=!0),o.collapsed)qn(c,t.line,r.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=r.line;u++)Fn(c,u,"text");o.atomic&&Vo(c.doc),Nr(c,"markerAdded",c,o)}return o}ga.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Ci(e),xe(this,"clear")){var r=this.find();r&&Nr(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],l=It(a.markedSpans,this);e&&!this.collapsed?Fn(e,rt(a),"text"):e&&(null!=l.to&&(i=rt(a)),null!=l.from&&(n=rt(a))),a.markedSpans=Pt(a.markedSpans,l),null==l.from&&this.collapsed&&!ir(this.doc,a)&&e&&tt(a,An(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var c=Jt(this.lines[s]),u=lr(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&qn(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Vo(e.doc)),e&&Nr(e,"markerCleared",e,this,n,i),t&&Si(e),this.parent&&this.parent.clear()}},ga.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=It(o.markedSpans,this);if(null!=a.from&&(r=at(t?o:rt(o),a.from),-1==e))return r;if(null!=a.to&&(n=at(t?o:rt(o),a.to),1==e))return n}return r&&{from:r,to:n}},ga.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&zi(n,(function(){var i=t.line,o=rt(t.line),a=tn(n,o);if(a&&(dn(a),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!ir(r.doc,i)&&null!=r.height){var l=r.height;r.height=null;var s=jr(r)-l;s&&tt(i,i.height+s)}Nr(n,"markerChanged",n,e)}))},ga.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=R(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},ga.prototype.detachLine=function(e){if(this.lines.splice(R(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},ke(ga);var ya=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function ba(e,t,r,n,i){(n=q(n)).shared=!1;var o=[va(e,t,r,n,i)],a=o[0],l=n.widgetNode;return bo(e,(function(e){l&&(n.widgetNode=l.cloneNode(!0)),o.push(va(e,pt(e,t),pt(e,r),n,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=Y(o)})),new ya(o,a)}function wa(e){return e.findMarks(at(e.first,0),e.clipPos(at(e.lastLine())),(function(e){return e.parent}))}function xa(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(lt(o,a)){var l=va(e,o,a,n.primary,n.primary.type);n.markers.push(l),l.parent=n}}}function ka(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];bo(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==R(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}ya.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Nr(this,"clear")}},ya.prototype.find=function(e,t){return this.primary.find(e,t)},ke(ya);var _a=0,Ca=function(e,t,r,n,i){if(!(this instanceof Ca))return new Ca(e,t,r,n,i);null==r&&(r=0),da.call(this,[new ua([new cr("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=at(r,0);this.sel=so(o),this.history=new _o(null),this.id=++_a,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),yo(this,{from:o,to:o,text:e}),jo(this,so(o),U)};Ca.prototype=ee(da.prototype,{constructor:Ca,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=et(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Ii((function(e){var t=at(this.first,0),r=this.first+this.size-1;Jo(this,{from:t,to:at(r,Qe(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&ci(this.cm,0,0),jo(this,so(t),U)})),replaceRange:function(e,t,r,n){oa(this,e,t=pt(this,t),r=r?pt(this,r):t,n)},getRange:function(e,t,r){var n=Je(this,pt(this,e),pt(this,t));return!1===r?n:""===r?n.join(""):n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(it(this,e))return Qe(this,e)},getLineNumber:function(e){return rt(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=Qe(this,e)),Jt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return pt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Ii((function(e,t,r){Ho(this,pt(this,"number"==typeof e?at(e,t||0):e),null,r)})),setSelection:Ii((function(e,t,r){Ho(this,pt(this,e),pt(this,t||e),r)})),extendSelection:Ii((function(e,t,r){Wo(this,pt(this,e),t&&pt(this,t),r)})),extendSelections:Ii((function(e,t){qo(this,mt(this,e),t)})),extendSelectionsBy:Ii((function(e,t){qo(this,mt(this,Z(this.sel.ranges,e)),t)})),setSelections:Ii((function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new ao(pt(this,e[i].anchor),pt(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),jo(this,lo(this.cm,n,t),r)}})),addSelection:Ii((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new ao(pt(this,e),pt(this,t||e))),jo(this,lo(this.cm,n,n.length-1),r)})),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=Je(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=Je(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Ii((function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var l=t&&"end"!=t&&ho(this,n,t),s=n.length-1;s>=0;s--)Jo(this,n[s]);l?Bo(this,l):this.cm&&si(this.cm)})),undo:Ii((function(){ta(this,"undo")})),redo:Ii((function(){ta(this,"redo")})),undoSelection:Ii((function(){ta(this,"undo",!0)})),redoSelection:Ii((function(){ta(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){var e=this;this.history=new _o(this.history),bo(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Io(this.history.done),undone:Io(this.history.undone)}},setHistory:function(e){var t=this.history=new _o(this.history);t.done=Io(e.done.slice(0),null,!0),t.undone=Io(e.undone.slice(0),null,!0)},setGutterMarker:Ii((function(e,t,r){return ca(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&ie(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:Ii((function(e){var t=this;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&ca(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,ie(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!it(this,e))return null;if(t=e,!(e=Qe(this,e)))return null}else if(null==(t=rt(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Ii((function(e,t,r){return ca(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(S(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:Ii((function(e,t,r){return ca(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(S(r));if(!o)return!1;var a=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0}))})),addLineWidget:Ii((function(e,t,r){return ha(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return va(this,pt(this,e),pt(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return va(this,e=pt(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=Qe(this,(e=pt(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=pt(this,e),t=pt(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||r&&!r(s.marker)||n.push(s.marker.parent||s.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),pt(this,at(r,t))},indexFromPos:function(e){var t=(e=pt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new Ca(et(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new Ca(et(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],xa(n,wa(this)),n},unlinkDoc:function(e){if(e instanceof ql&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),ka(wa(this));break}if(e.history==this.history){var r=[e.id];bo(e,(function(e){return r.push(e.id)}),!0),e.history=new _o(null),e.history.done=Io(this.history.done,r),e.history.undone=Io(this.history.undone,r)}},iterLinkedDocs:function(e){bo(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Ie(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Ii((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&ko(this.cm))}))}),Ca.prototype.eachLine=Ca.prototype.iter;var Sa=0;function Ma(e){var t=this;if(Na(t),!be(t,e)&&!Ur(t.display,e)){_e(e),a&&(Sa=+new Date);var r=Pn(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(){++l==i&&Di(t,(function(){var e={from:r=pt(t.doc,r),to:r,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};Jo(t.doc,e),Bo(t.doc,so(pt(t.doc,r),pt(t.doc,co(e))))}))()},c=function(e,r){if(t.options.allowDropFileTypes&&-1==R(t.options.allowDropFileTypes,e.type))s();else{var n=new FileReader;n.onerror=function(){return s()},n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[r]=e),s()},n.readAsText(e)}},u=0;u<n.length;u++)c(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var d=e.dataTransfer.getData("Text");if(d){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),Uo(t.doc,so(r,r)),f)for(var p=0;p<f.length;++p)oa(t.doc,"",f[p].anchor,f[p].head,"drag");t.replaceSelection(d,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Ta(e,t){if(a&&(!e.state.draggingText||+new Date-Sa<100))Me(t);else if(!be(e,t)&&!Ur(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!p)){var r=A("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",f&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),f&&r.parentNode.removeChild(r)}}function La(e,t){var r=Pn(e,t);if(r){var n=document.createDocumentFragment();Vn(e,r,n),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,n)}}function Na(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Aa(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation((function(){for(var t=0;t<r.length;t++)e(r[t])}))}}var Oa=!1;function za(){Oa||(Da(),Oa=!0)}function Da(){var e;me(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Aa(Ea)}),100))})),me(window,"blur",(function(){return Aa(Jn)}))}function Ea(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Ia={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Pa=0;Pa<10;Pa++)Ia[Pa+48]=Ia[Pa+96]=String(Pa);for(var Wa=65;Wa<=90;Wa++)Ia[Wa]=String.fromCharCode(Wa);for(var qa=1;qa<=12;qa++)Ia[qa+111]=Ia[qa+63235]="F"+qa;var Fa={};function Ha(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Ra(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=Z(r.split(" "),Ha),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=n):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[r]}for(var c in t)e[c]=t[c];return e}function Ba(e,t,r,n){var i=(t=Va(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Ba(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=Ba(e,t.fallthrough[o],r,n);if(a)return a}}}function ja(e){var t="string"==typeof e?e:Ia[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Ua(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(_?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(_?t.ctrlKey:t.metaKey)&&"Mod"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Ka(e,t){if(f&&34==e.keyCode&&e.char)return!1;var r=Ia[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),Ua(r,e,t))}function Va(e){return"string"==typeof e?Fa[e]:e}function $a(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&lt(o.from,Y(n).to)<=0;){var a=n.pop();if(lt(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}zi(e,(function(){for(var t=n.length-1;t>=0;t--)oa(e.doc,"",n[t].from,n[t].to,"+delete");si(e)}))}function Ga(e,t,r){var n=le(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Xa(e,t,r){var n=Ga(e,t.ch,r);return null==n?null:new at(t.line,n,r<0?"after":"before")}function Ya(e,t,r,n,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=pe(r,t.doc.direction);if(o){var a,l=i<0?Y(o):o[0],s=i<0==(1==l.level)?"after":"before";if(l.level>0||"rtl"==t.doc.direction){var c=rn(t,r);a=i<0?r.text.length-1:0;var u=nn(t,c,a).top;a=se((function(e){return nn(t,c,e).top==u}),i<0==(1==l.level)?l.from:l.to-1,a),"before"==s&&(a=Ga(r,a,1))}else a=i<0?l.to:l.from;return new at(n,a,s)}}return new at(n,i<0?r.text.length:0,i<0?"before":"after")}function Za(e,t,r,n){var i=pe(t,e.doc.direction);if(!i)return Xa(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=de(i,r.ch,r.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(n>0?a.to>r.ch:a.from<r.ch))return Xa(t,r,n);var l,s=function(e,r){return Ga(t,e instanceof at?e.ch:e,r)},c=function(r){return e.options.lineWrapping?(l=l||rn(e,t),Sn(e,t,l,r)):{begin:0,end:t.text.length}},u=c("before"==r.sticky?s(r,-1):r.ch);if("rtl"==e.doc.direction||1==a.level){var d=1==a.level==n<0,f=s(r,d?1:-1);if(null!=f&&(d?f<=a.to&&f<=u.end:f>=a.from&&f>=u.begin)){var p=d?"before":"after";return new at(r.line,f,p)}}var h=function(e,t,n){for(var o=function(e,t){return t?new at(r.line,s(e,1),"before"):new at(r.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],l=t>0==(1!=a.level),c=l?n.begin:s(n.end,-1);if(a.from<=c&&c<a.to)return o(c,l);if(c=l?a.from:s(a.to,-1),n.begin<=c&&c<n.end)return o(c,l)}},m=h(o+n,n,u);if(m)return m;var g=n>0?u.end:s(u.begin,-1);return null==g||n>0&&g==t.text.length||!(m=h(n>0?0:i.length-1,n,c(g)))?null:m}Fa.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Fa.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Fa.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Fa.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Fa.default=b?Fa.macDefault:Fa.pcDefault;var Qa={selectAll:Zo,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),U)},killLine:function(e){return $a(e,(function(t){if(t.empty()){var r=Qe(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:at(t.head.line+1,0)}:{from:t.head,to:at(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return $a(e,(function(t){return{from:at(t.from().line,0),to:pt(e.doc,at(t.to().line+1,0))}}))},delLineLeft:function(e){return $a(e,(function(e){return{from:at(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return $a(e,(function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return $a(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(at(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(at(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return Ja(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return tl(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return el(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),V)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),V)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?tl(e,t.head):n}),V)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=F(e.getLine(o.line),o.ch,n);t.push(X(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return zi(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=Qe(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new at(i.line,i.ch-1)),i.ch>0)i=new at(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),at(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=Qe(e.doc,i.line-1).text;a&&(i=new at(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),at(i.line-1,a.length-1),i,"+transpose"))}r.push(new ao(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return zi(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);si(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Ja(e,t){var r=Qe(e.doc,t),n=Jt(r);return n!=r&&(t=rt(n)),Ya(!0,e,n,t,1)}function el(e,t){var r=Qe(e.doc,t),n=er(r);return n!=r&&(t=rt(n)),Ya(!0,e,r,t,-1)}function tl(e,t){var r=Ja(e,t.line),n=Qe(e.doc,r.line),i=pe(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(r.ch,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return at(r.line,a?0:o,r.sticky)}return r}function rl(e,t,r){if("string"==typeof t&&!(t=Qa[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=j}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function nl(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Ba(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Ba(t,e.options.extraKeys,r,e)||Ba(t,e.options.keyMap,r,e)}var il=new H;function ol(e,t,r,n){var i=e.state.keySeq;if(i){if(ja(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:il.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),al(e,i+" "+t,r,n))return!0}return al(e,t,r,n)}function al(e,t,r,n){var i=nl(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&Nr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(_e(r),Xn(e)),!!i}function ll(e,t){var r=Ka(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?ol(e,"Shift-"+r,t,(function(t){return rl(e,t,!0)}))||ol(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return rl(e,t)})):ol(e,r,t,(function(t){return rl(e,t)})))}function sl(e,t,r){return ol(e,"'"+r+"'",t,(function(t){return rl(e,t,!0)}))}var cl=null;function ul(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=D(),be(t,e)))){a&&l<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var i=ll(t,e);f&&(cl=i?n:null,i||88!=n||We||!(b?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),r&&!b&&!i&&46==n&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||dl(t)}}function dl(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),ve(document,"keyup",r),ve(document,"mouseover",r))}E(t,"CodeMirror-crosshair"),me(document,"keyup",r),me(document,"mouseover",r)}function fl(e){16==e.keyCode&&(this.doc.sel.shift=!1),be(this,e)}function pl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||Ur(t.display,e)||be(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(f&&r==cl)return cl=null,void _e(e);if(!f||e.which&&!(e.which<10)||!ll(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(sl(t,e,i)||t.display.input.onKeyPress(e))}}}var hl,ml,gl=400,vl=function(e,t,r){this.time=e,this.pos=t,this.button=r};function yl(e,t){var r=+new Date;return ml&&ml.compare(r,e,t)?(hl=ml=null,"triple"):hl&&hl.compare(r,e,t)?(ml=new vl(r,e,t),hl=null,"double"):(hl=new vl(r,e,t),ml=null,"single")}function bl(e){var t=this,r=t.display;if(!(be(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,Ur(r,e))s||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!Ll(t,e)){var n=Pn(t,e),i=Le(e),o=n?yl(n,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&wl(t,i,n,o,e)||(1==i?n?kl(t,n,o,e):Te(e)==r.scroller&&_e(e):2==i?(n&&Wo(t.doc,n),setTimeout((function(){return r.input.focus()}),20)):3==i&&(C?t.display.input.onContextMenu(e):Zn(t)))}}function wl(e,t,r,n,i){var o="Click";return"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o),ol(e,Ua(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,(function(t){if("string"==typeof t&&(t=Qa[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=j}finally{e.state.suppressEdits=!1}return n}))}function xl(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=w?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey),null==i.addNew&&(i.addNew=b?r.metaKey:r.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?r.altKey:r.ctrlKey)),i}function kl(e,t,r,n){a?setTimeout(W(Yn,e),0):e.curOp.focus=D();var i,o=xl(e,r,n),l=e.doc.sel;e.options.dragDrop&&Oe&&!e.isReadOnly()&&"single"==r&&(i=l.contains(t))>-1&&(lt((i=l.ranges[i]).from(),t)<0||t.xRel>0)&&(lt(i.to(),t)>0||t.xRel<0)?_l(e,n,t,o):Sl(e,n,t,o)}function _l(e,t,r,n){var i=e.display,o=!1,c=Di(e,(function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Zn(e)),ve(i.wrapper.ownerDocument,"mouseup",c),ve(i.wrapper.ownerDocument,"mousemove",u),ve(i.scroller,"dragstart",d),ve(i.scroller,"drop",c),o||(_e(t),n.addNew||Wo(e.doc,r,null,null,n.extend),s&&!p||a&&9==l?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},d=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=c,c.copy=!n.moveOnDrag,me(i.wrapper.ownerDocument,"mouseup",c),me(i.wrapper.ownerDocument,"mousemove",u),me(i.scroller,"dragstart",d),me(i.scroller,"drop",c),e.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Cl(e,t,r){if("char"==r)return new ao(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new ao(at(t.line,0),pt(e.doc,at(t.line+1,0)));var n=r(e,t);return new ao(n.from,n.to)}function Sl(e,t,r,n){a&&Zn(e);var i=e.display,o=e.doc;_e(t);var l,s,c=o.sel,u=c.ranges;if(n.addNew&&!n.extend?(s=o.sel.contains(r),l=s>-1?u[s]:new ao(r,r)):(l=o.sel.primary(),s=o.sel.primIndex),"rectangle"==n.unit)n.addNew||(l=new ao(r,r)),r=Pn(e,t,!0,!0),s=-1;else{var d=Cl(e,r,n.unit);l=n.extend?Po(l,d.anchor,d.head,n.extend):d}n.addNew?-1==s?(s=u.length,jo(o,lo(e,u.concat([l]),s),{scroll:!1,origin:"*mouse"})):u.length>1&&u[s].empty()&&"char"==n.unit&&!n.extend?(jo(o,lo(e,u.slice(0,s).concat(u.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):Fo(o,s,l,K):(s=0,jo(o,new oo([l],0),K),c=o.sel);var f=r;function p(t){if(0!=lt(f,t))if(f=t,"rectangle"==n.unit){for(var i=[],a=e.options.tabSize,u=F(Qe(o,r.line).text,r.ch,a),d=F(Qe(o,t.line).text,t.ch,a),p=Math.min(u,d),h=Math.max(u,d),m=Math.min(r.line,t.line),g=Math.min(e.lastLine(),Math.max(r.line,t.line));m<=g;m++){var v=Qe(o,m).text,y=$(v,p,a);p==h?i.push(new ao(at(m,y),at(m,y))):v.length>y&&i.push(new ao(at(m,y),at(m,$(v,h,a))))}i.length||i.push(new ao(r,r)),jo(o,lo(e,c.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=l,x=Cl(e,t,n.unit),k=w.anchor;lt(x.anchor,k)>0?(b=x.head,k=dt(w.from(),x.anchor)):(b=x.anchor,k=ut(w.to(),x.head));var _=c.ranges.slice(0);_[s]=Ml(e,new ao(pt(o,k),b)),jo(o,lo(e,_,s),K)}}var h=i.wrapper.getBoundingClientRect(),m=0;function g(t){var r=++m,a=Pn(e,t,!0,"rectangle"==n.unit);if(a)if(0!=lt(a,f)){e.curOp.focus=D(),p(a);var l=ri(i,o);(a.line>=l.to||a.line<l.from)&&setTimeout(Di(e,(function(){m==r&&g(t)})),150)}else{var s=t.clientY<h.top?-20:t.clientY>h.bottom?20:0;s&&setTimeout(Di(e,(function(){m==r&&(i.scroller.scrollTop+=s,g(t))})),50)}}function v(t){e.state.selectingText=!1,m=1/0,t&&(_e(t),i.input.focus()),ve(i.wrapper.ownerDocument,"mousemove",y),ve(i.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=Di(e,(function(e){0!==e.buttons&&Le(e)?g(e):v(e)})),b=Di(e,v);e.state.selectingText=b,me(i.wrapper.ownerDocument,"mousemove",y),me(i.wrapper.ownerDocument,"mouseup",b)}function Ml(e,t){var r=t.anchor,n=t.head,i=Qe(e.doc,r.line);if(0==lt(r,n)&&r.sticky==n.sticky)return t;var o=pe(i);if(!o)return t;var a=de(o,r.ch,r.sticky),l=o[a];if(l.from!=r.ch&&l.to!=r.ch)return t;var s,c=a+(l.from==r.ch==(1!=l.level)?0:1);if(0==c||c==o.length)return t;if(n.line!=r.line)s=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=de(o,n.ch,n.sticky),d=u-a||(n.ch-r.ch)*(1==l.level?-1:1);s=u==c-1||u==c?d<0:d>0}var f=o[c+(s?-1:0)],p=s==(1==f.level),h=p?f.from:f.to,m=p?"after":"before";return r.ch==h&&r.sticky==m?t:new ao(new at(r.line,h,m),n)}function Tl(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&_e(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!xe(e,r))return Se(t);o-=l.top-a.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var c=a.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i)return ye(e,r,e,nt(e.doc,o),e.display.gutterSpecs[s].className,t),Se(t)}}function Ll(e,t){return Tl(e,t,"gutterClick",!0)}function Nl(e,t){Ur(e.display,t)||Al(e,t)||be(e,t,"contextmenu")||C||e.display.input.onContextMenu(t)}function Al(e,t){return!!xe(e,"gutterContextMenu")&&Tl(e,t,"gutterContextMenu",!1)}function Ol(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),pn(e)}vl.prototype.compare=function(e,t,r){return this.time+gl>e&&0==lt(t,this.pos)&&r==this.button};var zl={toString:function(){return"CodeMirror.Init"}},Dl={},El={};function Il(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=zl&&i(e,t,r)}:i)}e.defineOption=r,e.Init=zl,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,mo(e)}),!0),r("indentUnit",2,mo,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){go(e),pn(e),qn(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(at(n,o))}n++}));for(var i=r.length-1;i>=0;i--)oa(e.doc,t,r[i],at(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=zl&&e.refresh()})),r("specialCharPlaceholder",gr,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),r("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),r("rtlMoveVisually",!x),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){Ol(e),Qi(e)}),!0),r("keyMap","default",(function(e,t,r){var n=Va(t),i=r!=zl&&Va(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("configureMouse",null),r("lineWrapping",!1,Wl,!0),r("gutters",[],(function(e,t){e.display.gutterSpecs=Yi(t,e.options.lineNumbers),Qi(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?Dn(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return bi(e)}),!0),r("scrollbarStyle","native",(function(e){ki(e),bi(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=Yi(e.options.gutters,t),Qi(e)}),!0),r("firstLineNumber",1,Qi,!0),r("lineNumberFormatter",(function(e){return e}),Qi,!0),r("showCursorWhenSelecting",!1,Un,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("pasteLinesPerSelection",!0),r("selectionsMayTouch",!1),r("readOnly",!1,(function(e,t){"nocursor"==t&&(Jn(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),r("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,Pl),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,Un,!0),r("singleCursorHeightPerLine",!0,Un,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,go,!0),r("addModeClass",!1,go,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,go,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null),r("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),r("phrases",null)}function Pl(e,t,r){if(!t!=!(r&&r!=zl)){var n=e.display.dragFunctions,i=t?me:ve;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function Wl(e){e.options.lineWrapping?(E(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),sr(e)),In(e),qn(e),pn(e),setTimeout((function(){return bi(e)}),100)}function ql(e,t){var r=this;if(!(this instanceof ql))return new ql(e,t);this.options=t=t?q(t):{},q(Dl,t,!1);var n=t.value;"string"==typeof n?n=new Ca(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new ql.inputStyles[t.inputStyle](this),o=this.display=new Ji(e,n,i,t);for(var c in o.wrapper.CodeMirror=this,Ol(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),ki(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new H,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),a&&l<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),Fl(this),za(),Ci(this),this.curOp.forceUpdate=!0,wo(this,n),t.autofocus&&!y||this.hasFocus()?setTimeout((function(){r.hasFocus()&&!r.state.focused&&Qn(r)}),20):Jn(this),El)El.hasOwnProperty(c)&&El[c](this,t[c],zl);Xi(this),t.finishInit&&t.finishInit(this);for(var u=0;u<Hl.length;++u)Hl[u](this);Si(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Fl(e){var t=e.display;me(t.scroller,"mousedown",Di(e,bl)),me(t.scroller,"dblclick",a&&l<11?Di(e,(function(t){if(!be(e,t)){var r=Pn(e,t);if(r&&!Ll(e,t)&&!Ur(e.display,t)){_e(t);var n=e.findWordAt(r);Wo(e.doc,n.anchor,n.head)}}})):function(t){return be(e,t)||_e(t)}),me(t.scroller,"contextmenu",(function(t){return Nl(e,t)})),me(t.input.getField(),"contextmenu",(function(r){t.scroller.contains(r.target)||Nl(e,r)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),(n=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}me(t.scroller,"touchstart",(function(i){if(!be(e,i)&&!o(i)&&!Ll(e,i)){t.input.ensurePolled(),clearTimeout(r);var a=+new Date;t.activeTouch={start:a,moved:!1,prev:a-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),me(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),me(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!Ur(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!n.prev||s(n,n.prev)?new ao(a,a):!n.prev.prev||s(n,n.prev.prev)?e.findWordAt(a):new ao(at(a.line,0),pt(e.doc,at(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),_e(r)}i()})),me(t.scroller,"touchcancel",i),me(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(pi(e,t.scroller.scrollTop),mi(e,t.scroller.scrollLeft,!0),ye(e,"scroll",e))})),me(t.scroller,"mousewheel",(function(t){return io(e,t)})),me(t.scroller,"DOMMouseScroll",(function(t){return io(e,t)})),me(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){be(e,t)||Me(t)},over:function(t){be(e,t)||(La(e,t),Me(t))},start:function(t){return Ta(e,t)},drop:Di(e,Ma),leave:function(t){be(e,t)||Na(e)}};var c=t.input.getField();me(c,"keyup",(function(t){return fl.call(e,t)})),me(c,"keydown",Di(e,ul)),me(c,"keypress",Di(e,pl)),me(c,"focus",(function(t){return Qn(e,t)})),me(c,"blur",(function(t){return Jn(e,t)}))}ql.defaults=Dl,ql.optionHandlers=El;var Hl=[];function Rl(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=wt(e,t).state:r="prev");var a=e.options.tabSize,l=Qe(o,t),s=F(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var c,u=l.text.match(/^\s*/)[0];if(n||/\S/.test(l.text)){if("smart"==r&&((c=o.mode.indent(i,l.text.slice(u.length),l.text))==j||c>150)){if(!n)return;r="prev"}}else c=0,r="not";"prev"==r?c=t>o.first?F(Qe(o,t-1).text,null,a):0:"add"==r?c=s+e.options.indentUnit:"subtract"==r?c=s-e.options.indentUnit:"number"==typeof r&&(c=s+r),c=Math.max(0,c);var d="",f=0;if(e.options.indentWithTabs)for(var p=Math.floor(c/a);p;--p)f+=a,d+="\t";if(f<c&&(d+=X(c-f)),d!=u)return oa(o,d,at(t,0),at(t,u.length),"+input"),l.stateAfter=null,!0;for(var h=0;h<o.sel.ranges.length;h++){var m=o.sel.ranges[h];if(m.head.line==t&&m.head.ch<u.length){var g=at(t,u.length);Fo(o,h,new ao(g,g));break}}}ql.defineInitHook=function(e){return Hl.push(e)};var Bl=null;function jl(e){Bl=e}function Ul(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a=+new Date-200,l="paste"==i||e.state.pasteIncoming>a,s=Ie(t),c=null;if(l&&n.ranges.length>1)if(Bl&&Bl.text.join("\n")==t){if(n.ranges.length%Bl.text.length==0){c=[];for(var u=0;u<Bl.text.length;u++)c.push(o.splitLines(Bl.text[u]))}}else s.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(c=Z(s,(function(e){return[e]})));for(var d=e.curOp.updateInput,f=n.ranges.length-1;f>=0;f--){var p=n.ranges[f],h=p.from(),m=p.to();p.empty()&&(r&&r>0?h=at(h.line,h.ch-r):e.state.overwrite&&!l?m=at(m.line,Math.min(Qe(o,m.line).text.length,m.ch+Y(s).length)):l&&Bl&&Bl.lineWise&&Bl.text.join("\n")==s.join("\n")&&(h=m=at(h.line,0)));var g={from:h,to:m,text:c?c[f%c.length]:s,origin:i||(l?"paste":e.state.cutIncoming>a?"cut":"+input")};Jo(e.doc,g),Nr(e,"inputRead",e,g)}t&&!l&&Vl(e,t),si(e),e.curOp.updateInput<2&&(e.curOp.updateInput=d),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Kl(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||zi(t,(function(){return Ul(t,r,0,null,"paste")})),!0}function Vl(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=Rl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Qe(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Rl(e,i.head.line,"smart"));a&&Nr(e,"electricInput",e,i.head.line)}}}function $l(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:at(i,0),head:at(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Gl(e,t,r,n){e.setAttribute("autocorrect",r?"":"off"),e.setAttribute("autocapitalize",n?"":"off"),e.setAttribute("spellcheck",!!t)}function Xl(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),Gl(e),t}function Yl(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Di(this,t[e])(this,r,i),ye(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Va(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Ei((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");Q(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,qn(this)})),removeOverlay:Ei((function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void qn(this)}})),indentLine:Ei((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),it(this.doc,e)&&Rl(this,e,t,r)})),indentSelection:Ei((function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(Rl(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&si(this));else{var o=i.from(),a=i.to(),l=Math.max(r,o.line);r=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var s=l;s<r;++s)Rl(this,s,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[n].from().ch>0&&Fo(this.doc,n,new ao(o,c[n].to()),U)}}})),getTokenAt:function(e,t){return St(this,e,t)},getLineTokens:function(e,t){return St(this,at(e),t,!0)},getTokenTypeAt:function(e){e=pt(this.doc,e);var t,r=bt(this,Qe(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var l=i[o[t][a]];l&&n.push(l)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var s=0;s<i._global.length;s++){var c=i._global[s];c.pred(o,this)&&-1==R(n,c.val)&&n.push(c.val)}return n},getStateAfter:function(e,t){var r=this.doc;return wt(this,(e=ft(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return wn(this,null==e?r.head:"object"==typeof e?pt(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return bn(this,pt(this.doc,e),t||"page")},coordsChar:function(e,t){return _n(this,(e=yn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=yn(this,{top:e,left:0},t||"page").top,nt(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=Qe(this.doc,e)}else n=e;return vn(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-ar(n):0)},defaultTextHeight:function(){return An(this.display)},defaultCharWidth:function(){return On(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display,a=(e=wn(this,pt(this.doc,e))).bottom,l=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)a=e.top;else if("above"==n||"near"==n){var s=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>s)&&e.top>t.offsetHeight?a=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=s&&(a=e.bottom),l+t.offsetWidth>c&&(l=c-t.offsetWidth)}t.style.top=a+"px",t.style.left=t.style.right="","right"==i?(l=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=l+"px"),r&&oi(this,{left:l,top:a,right:l+t.offsetWidth,bottom:a+t.offsetHeight})},triggerOnKeyDown:Ei(ul),triggerOnKeyPress:Ei(pl),triggerOnKeyUp:fl,triggerOnMouseDown:Ei(bl),execCommand:function(e){if(Qa.hasOwnProperty(e))return Qa[e].call(null,this)},triggerElectric:Ei((function(e){Vl(this,e)})),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=pt(this.doc,e),a=0;a<t&&!(o=Zl(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Ei((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?Zl(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),V)})),deleteH:Ei((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):$a(this,(function(r){var i=Zl(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=pt(this.doc,e),l=0;l<t;++l){var s=wn(this,a,"div");if(null==o?o=s.left:s.left=o,(a=Ql(this,s,i,r)).hitSide)break}return a},moveV:Ei((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(a){if(o)return e<0?a.from():a.to();var l=wn(r,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=Ql(r,l,e,t);return"page"==t&&a==n.sel.primary()&&li(r,bn(r,s,"div").top-l.top),s}),V),i.length)for(var a=0;a<n.sel.ranges.length;a++)n.sel.ranges[a].goalColumn=i[a]})),findWordAt:function(e){var t=Qe(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),a=ne(o,i)?function(e){return ne(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!ne(e)};r>0&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new ao(at(e.line,r),at(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?E(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),ye(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Ei((function(e,t){ci(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Gr(this)-this.display.barHeight,width:e.scrollWidth-Gr(this)-this.display.barWidth,clientHeight:Yr(this),clientWidth:Xr(this)}},scrollIntoView:Ei((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:at(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?ui(this,e):fi(this,e.from,e.to,e.margin)})),setSize:Ei((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&fn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Fn(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,ye(this,"refresh",this)})),operation:function(e){return zi(this,e)},startOperation:function(){return Ci(this)},endOperation:function(){return Si(this)},refresh:Ei((function(){var e=this.display.cachedTextHeight;qn(this),this.curOp.forceUpdate=!0,pn(this),ci(this,this.doc.scrollLeft,this.doc.scrollTop),Vi(this.display),(null==e||Math.abs(e-An(this.display))>.5||this.options.lineWrapping)&&In(this),ye(this,"refresh",this)})),swapDoc:Ei((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),wo(this,e),pn(this),this.display.input.reset(),ci(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Nr(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},ke(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}function Zl(e,t,r,n,i){var o=t,a=r,l=Qe(e,t.line),s=i&&"rtl"==e.direction?-r:r;function c(){var r=t.line+s;return!(r<e.first||r>=e.first+e.size)&&(t=new at(r,t.ch,t.sticky),l=Qe(e,r))}function u(o){var a;if("codepoint"==n){var u=l.text.charCodeAt(t.ch+(r>0?0:-1));if(isNaN(u))a=null;else{var d=r>0?u>=55296&&u<56320:u>=56320&&u<57343;a=new at(t.line,Math.max(0,Math.min(l.text.length,t.ch+r*(d?2:1))),-r)}}else a=i?Za(e.cm,l,t,r):Xa(l,t,r);if(null==a){if(o||!c())return!1;t=Ya(i,e.cm,l,t.line,s)}else t=a;return!0}if("char"==n||"codepoint"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var d=null,f="group"==n,p=e.cm&&e.cm.getHelper(t,"wordChars"),h=!0;!(r<0)||u(!h);h=!1){var m=l.text.charAt(t.ch)||"\n",g=ne(m,p)?"w":f&&"\n"==m?"n":!f||/\s/.test(m)?null:"p";if(!f||h||g||(g="s"),d&&d!=g){r<0&&(r=1,u(),t.sticky="after");break}if(g&&(d=g),r>0&&!u(!h))break}var v=Xo(e,t,o,a,!0);return st(o,v)&&(v.hitSide=!0),v}function Ql(e,t,r,n){var i,o,a=e.doc,l=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),c=Math.max(s-.5*An(e.display),3);i=(r>0?t.bottom:t.top)+r*c}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=_n(e,l,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}var Jl=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new H,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function es(e,t){var r=tn(e,t.line);if(!r||r.hidden)return null;var n=Qe(e.doc,t.line),i=Qr(r,n,t.line),o=pe(n,e.doc.direction),a="left";o&&(a=de(o,t.ch)%2?"right":"left");var l=ln(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function ts(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function rs(e,t){return t&&(e.bad=!0),e}function ns(e,t,r,n,i){var o="",a=!1,l=e.doc.lineSeparator(),s=!1;function c(e){return function(t){return t.id==e}}function u(){a&&(o+=l,s&&(o+=l),a=s=!1)}function d(e){e&&(u(),o+=e)}function f(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(r)return void d(r);var o,p=t.getAttribute("cm-marker");if(p){var h=e.findMarks(at(n,0),at(i+1,0),c(+p));return void(h.length&&(o=h[0].find(0))&&d(Je(e.doc,o.from,o.to).join(l)))}if("false"==t.getAttribute("contenteditable"))return;var m=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;m&&u();for(var g=0;g<t.childNodes.length;g++)f(t.childNodes[g]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),m&&(a=!0)}else 3==t.nodeType&&d(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;f(t),t!=r;)t=t.nextSibling,s=!1;return o}function is(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return rs(e.clipPos(at(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return os(o,t,r)}}function os(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!z(n,t))return rs(at(rt(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?Y(e.rest):e.line;return rs(at(rt(o),o.text.length),i)}var a=3==t.nodeType?t:null,l=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));l.parentNode!=n;)l=l.parentNode;var s=e.measure,c=s.maps;function u(t,r,n){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?s.map:c[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==r){var u=rt(i<0?e.line:e.rest[i]),d=o[a]+n;return(n<0||l!=t)&&(d=o[a+(n?1:0)]),at(u,d)}}}var d=u(a,l,r);if(d)return rs(d,i);for(var f=l.nextSibling,p=a?a.nodeValue.length-r:0;f;f=f.nextSibling){if(d=u(f,f.firstChild,0))return rs(at(d.line,d.ch-p),i);p+=f.textContent.length}for(var h=l.previousSibling,m=r;h;h=h.previousSibling){if(d=u(h,h.firstChild,-1))return rs(at(d.line,d.ch+m),i);m+=h.textContent.length}}Jl.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function a(e){if(o(e)&&!be(n,e)){if(n.somethingSelected())jl({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=$l(n);jl({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,U),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var a=Bl.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a)return void e.preventDefault()}var l=Xl(),s=l.firstChild;n.display.lineSpace.insertBefore(l,n.display.lineSpace.firstChild),s.value=Bl.text.join("\n");var c=D();P(s),setTimeout((function(){n.display.lineSpace.removeChild(l),c.focus(),c==i&&r.showPrimarySelection()}),50)}}i.contentEditable=!0,Gl(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),me(i,"paste",(function(e){!o(e)||be(n,e)||Kl(e,n)||l<=11&&setTimeout(Di(n,(function(){return t.updateFromDOM()})),20)})),me(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),me(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),me(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),me(i,"touchstart",(function(){return r.forceCompositionEnd()})),me(i,"input",(function(){t.composing||t.readFromDOMSoon()})),me(i,"copy",a),me(i,"cut",a)},Jl.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},Jl.prototype.prepareSelection=function(){var e=Kn(this.cm,!1);return e.focus=D()==this.div,e},Jl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Jl.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Jl.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=is(t,e.anchorNode,e.anchorOffset),l=is(t,e.focusNode,e.focusOffset);if(!a||a.bad||!l||l.bad||0!=lt(dt(a,l),i)||0!=lt(ut(a,l),o)){var s=t.display.view,c=i.line>=t.display.viewFrom&&es(t,i)||{node:s[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&es(t,o);if(!u){var d=s[s.length-1].measure,f=d.maps?d.maps[d.maps.length-1]:d.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(c&&u){var p,h=e.rangeCount&&e.getRangeAt(0);try{p=M(c.node,c.offset,u.offset,u.node)}catch(e){}p&&(!r&&t.state.focused?(e.collapse(c.node,c.offset),p.collapsed||(e.removeAllRanges(),e.addRange(p))):(e.removeAllRanges(),e.addRange(p)),h&&null==e.anchorNode?e.addRange(h):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Jl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},Jl.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Jl.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Jl.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return z(this.div,t)},Jl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&D()==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Jl.prototype.blur=function(){this.div.blur()},Jl.prototype.getField=function(){return this.div},Jl.prototype.supportsTouch=function(){return!0},Jl.prototype.receivedFocus=function(){var e=this,t=this;function r(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,r))}this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):zi(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,r)},Jl.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Jl.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&u&&this.cm.display.gutterSpecs.length&&ts(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=is(t,e.anchorNode,e.anchorOffset),n=is(t,e.focusNode,e.focusOffset);r&&n&&zi(t,(function(){jo(t.doc,so(r,n),U),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}}},Jl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=at(a.line-1,Qe(n.doc,a.line-1).length)),l.ch==Qe(n.doc,l.line).text.length&&l.line<n.lastLine()&&(l=at(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=Wn(n,a.line))?(t=rt(i.view[0].line),r=i.view[0].node):(t=rt(i.view[e].line),r=i.view[e-1].node.nextSibling);var s,c,u=Wn(n,l.line);if(u==i.view.length-1?(s=i.viewTo-1,c=i.lineDiv.lastChild):(s=rt(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!r)return!1;for(var d=n.doc.splitLines(ns(n,r,c,t,s)),f=Je(n.doc,at(t,0),at(s,Qe(n.doc,s).text.length));d.length>1&&f.length>1;)if(Y(d)==Y(f))d.pop(),f.pop(),s--;else{if(d[0]!=f[0])break;d.shift(),f.shift(),t++}for(var p=0,h=0,m=d[0],g=f[0],v=Math.min(m.length,g.length);p<v&&m.charCodeAt(p)==g.charCodeAt(p);)++p;for(var y=Y(d),b=Y(f),w=Math.min(y.length-(1==d.length?p:0),b.length-(1==f.length?p:0));h<w&&y.charCodeAt(y.length-h-1)==b.charCodeAt(b.length-h-1);)++h;if(1==d.length&&1==f.length&&t==a.line)for(;p&&p>a.ch&&y.charCodeAt(y.length-h-1)==b.charCodeAt(b.length-h-1);)p--,h++;d[d.length-1]=y.slice(0,y.length-h).replace(/^\u200b+/,""),d[0]=d[0].slice(p).replace(/\u200b+$/,"");var x=at(t,p),k=at(s,f.length?Y(f).length-h:0);return d.length>1||d[0]||lt(x,k)?(oa(n.doc,d,x,k,"+input"),!0):void 0},Jl.prototype.ensurePolled=function(){this.forceCompositionEnd()},Jl.prototype.reset=function(){this.forceCompositionEnd()},Jl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Jl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},Jl.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||zi(this.cm,(function(){return qn(e.cm)}))},Jl.prototype.setUneditable=function(e){e.contentEditable="false"},Jl.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Di(this.cm,Ul)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Jl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Jl.prototype.onContextMenu=function(){},Jl.prototype.resetPosition=function(){},Jl.prototype.needsContentAttribute=!0;var as=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new H,this.hasSelection=!1,this.composing=null};function ls(e,t){if((t=t?q(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=D();t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=l.getValue()}var i;if(e.form&&(me(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(r){r.save=n,r.getTextArea=function(){return e},r.toTextArea=function(){r.toTextArea=isNaN,n(),e.parentNode.removeChild(r.getWrapperElement()),e.style.display="",e.form&&(ve(e.form,"submit",n),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var l=ql((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return l}function ss(e){e.off=ve,e.on=me,e.wheelEventPixels=no,e.Doc=Ca,e.splitLines=Ie,e.countColumn=F,e.findColumn=$,e.isWordChar=re,e.Pass=j,e.signal=ye,e.Line=cr,e.changeEnd=co,e.scrollbarModel=xi,e.Pos=at,e.cmpPos=lt,e.modes=He,e.mimeModes=Re,e.resolveMode=Ue,e.getMode=Ke,e.modeExtensions=Ve,e.extendMode=$e,e.copyState=Ge,e.startState=Ye,e.innerMode=Xe,e.commands=Qa,e.keyMap=Fa,e.keyName=Ka,e.isModifierKey=ja,e.lookupKey=Ba,e.normalizeKeyMap=Ra,e.StringStream=Ze,e.SharedTextMarker=ya,e.TextMarker=ga,e.LineWidget=fa,e.e_preventDefault=_e,e.e_stopPropagation=Ce,e.e_stop=Me,e.addClass=E,e.contains=z,e.rmClass=T,e.keyNames=Ia}as.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!be(n,e)){if(n.somethingSelected())jl({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=$l(n);jl({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,U):(r.prevInput="",i.value=t.text.join("\n"),P(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),me(i,"input",(function(){a&&l>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),me(i,"paste",(function(e){be(n,e)||Kl(e,n)||(n.state.pasteIncoming=+new Date,r.fastPoll())})),me(i,"cut",o),me(i,"copy",o),me(e.scroller,"paste",(function(t){if(!Ur(e,t)&&!be(n,t)){if(!i.dispatchEvent)return n.state.pasteIncoming=+new Date,void r.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),me(e.lineSpace,"selectstart",(function(t){Ur(e,t)||_e(t)})),me(i,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),me(i,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},as.prototype.createField=function(e){this.wrapper=Xl(),this.textarea=this.wrapper.firstChild},as.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},as.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Kn(e);if(e.options.moveInputWithCursor){var i=wn(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},as.prototype.showSelection=function(e){var t=this.cm.display;N(t.cursorDiv,e.cursors),N(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},as.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&P(this.textarea),a&&l>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",a&&l>=9&&(this.hasSelection=null))}},as.prototype.getField=function(){return this.textarea},as.prototype.supportsTouch=function(){return!1},as.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||D()!=this.textarea))try{this.textarea.focus()}catch(e){}},as.prototype.blur=function(){this.textarea.blur()},as.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},as.prototype.receivedFocus=function(){this.slowPoll()},as.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},as.prototype.fastPoll=function(){var e=!1,t=this;function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},as.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||Pe(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(a&&l>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,c=Math.min(n.length,i.length);s<c&&n.charCodeAt(s)==i.charCodeAt(s);)++s;return zi(t,(function(){Ul(t,i.slice(s),n.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},as.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},as.prototype.onKeyPress=function(){a&&l>=9&&(this.hasSelection=null),this.fastPoll()},as.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=Pn(r,e),c=n.scroller.scrollTop;if(o&&!f){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(o)&&Di(r,jo)(r.doc,so(o),U);var u,d=i.style.cssText,p=t.wrapper.style.cssText,h=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-h.top-5)+"px; left: "+(e.clientX-h.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(u=window.scrollY),n.input.focus(),s&&window.scrollTo(null,u),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),a&&l>=9&&g(),C){Me(e);var m=function(){ve(window,"mouseup",m),setTimeout(v,20)};me(window,"mouseup",m)}else setTimeout(v,50)}function g(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=p,i.style.cssText=d,a&&l<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=c),null!=i.selectionStart)){(!a||a&&l<9)&&g();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Di(r,Zo)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},as.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},as.prototype.setUneditable=function(){},as.prototype.needsContentAttribute=!1,Il(ql),Yl(ql);var cs="iter insert remove copy getEditor constructor".split(" ");for(var us in Ca.prototype)Ca.prototype.hasOwnProperty(us)&&R(cs,us)<0&&(ql.prototype[us]=function(e){return function(){return e.apply(this.doc,arguments)}}(Ca.prototype[us]));return ke(Ca),ql.inputStyles={textarea:as,contenteditable:Jl},ql.defineMode=function(e){ql.defaults.mode||"null"==e||(ql.defaults.mode=e),Be.apply(this,arguments)},ql.defineMIME=je,ql.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),ql.defineMIME("text/plain","null"),ql.defineExtension=function(e,t){ql.prototype[e]=t},ql.defineDocExtension=function(e,t){Ca.prototype[e]=t},ql.fromTextArea=ls,ss(ql),ql.version="5.65.5",ql}()},762:(e,t,r)=>{!function(e){"use strict";function t(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.info=n,this.align=i,this.prev=o}function r(e,r,n,i){var o=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=n&&(o=e.context.indented),e.context=new t(o,r,n,i,null,e.context)}function n(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function i(e,t,r){return"variable"==t.prevToken||"type"==t.prevToken||!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,r))||!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0}function o(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function a(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function l(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}e.defineMode("clike",(function(a,s){var c,u,d=a.indentUnit,f=s.statementIndentUnit||d,p=s.dontAlignCalls,h=s.keywords||{},m=s.types||{},g=s.builtin||{},v=s.blockKeywords||{},y=s.defKeywords||{},b=s.atoms||{},w=s.hooks||{},x=s.multiLineStrings,k=!1!==s.indentStatements,_=!1!==s.indentSwitch,C=s.namespaceSeparator,S=s.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,M=s.numberStart||/[\d\.]/,T=s.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,L=s.isOperatorChar||/[+\-*&%=<>!?|\/]/,N=s.isIdentifierChar||/[\w\$_\xa1-\uffff]/,A=s.isReservedIdentifier||!1;function O(e,t){var r=e.next();if(w[r]){var n=w[r](e,t);if(!1!==n)return n}if('"'==r||"'"==r)return t.tokenize=z(r),t.tokenize(e,t);if(M.test(r)){if(e.backUp(1),e.match(T))return"number";e.next()}if(S.test(r))return c=r,null;if("/"==r){if(e.eat("*"))return t.tokenize=D,D(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(L.test(r)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(L););return"operator"}if(e.eatWhile(N),C)for(;e.match(C);)e.eatWhile(N);var i=e.current();return l(h,i)?(l(v,i)&&(c="newstatement"),l(y,i)&&(u=!0),"keyword"):l(m,i)?"type":l(g,i)||A&&A(i)?(l(v,i)&&(c="newstatement"),"builtin"):l(b,i)?"atom":"variable"}function z(e){return function(t,r){for(var n,i=!1,o=!1;null!=(n=t.next());){if(n==e&&!i){o=!0;break}i=!i&&"\\"==n}return(o||!i&&!x)&&(r.tokenize=null),"string"}}function D(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function E(e,t){s.typeFirstDefinitions&&e.eol()&&o(t.context)&&(t.typeAtEndOfLine=i(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new t((e||0)-d,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var a=t.context;if(e.sol()&&(null==a.align&&(a.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return E(e,t),null;c=u=null;var l=(t.tokenize||O)(e,t);if("comment"==l||"meta"==l)return l;if(null==a.align&&(a.align=!0),";"==c||":"==c||","==c&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)n(t);else if("{"==c)r(t,e.column(),"}");else if("["==c)r(t,e.column(),"]");else if("("==c)r(t,e.column(),")");else if("}"==c){for(;"statement"==a.type;)a=n(t);for("}"==a.type&&(a=n(t));"statement"==a.type;)a=n(t)}else c==a.type?n(t):k&&(("}"==a.type||"top"==a.type)&&";"!=c||"statement"==a.type&&"newstatement"==c)&&r(t,e.column(),"statement",e.current());if("variable"==l&&("def"==t.prevToken||s.typeFirstDefinitions&&i(e,t,e.start)&&o(t.context)&&e.match(/^\s*\(/,!1))&&(l="def"),w.token){var d=w.token(e,t,l);void 0!==d&&(l=d)}return"def"==l&&!1===s.styleDefs&&(l="variable"),t.startOfLine=!1,t.prevToken=u?"def":l||c,E(e,t),l},indent:function(t,r){if(t.tokenize!=O&&null!=t.tokenize||t.typeAtEndOfLine)return e.Pass;var n=t.context,i=r&&r.charAt(0),o=i==n.type;if("statement"==n.type&&"}"==i&&(n=n.prev),s.dontIndentStatements)for(;"statement"==n.type&&s.dontIndentStatements.test(n.info);)n=n.prev;if(w.indent){var a=w.indent(t,n,r,d);if("number"==typeof a)return a}var l=n.prev&&"switch"==n.prev.info;if(s.allmanIndentation&&/[{(]/.test(i)){for(;"top"!=n.type&&"}"!=n.type;)n=n.prev;return n.indented}return"statement"==n.type?n.indented+("{"==i?0:f):!n.align||p&&")"==n.type?")"!=n.type||o?n.indented+(o?0:d)+(o||!l||/^(?:case|default)\b/.test(r)?0:d):n.indented+f:n.column+(o?0:1)},electricInput:_?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}}));var s="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",c="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",u="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",d="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",f=a("int long char short double float unsigned signed void bool"),p=a("SEL instancetype id Class Protocol BOOL");function h(e){return l(f,e)||/.+_t$/.test(e)}function m(e){return h(e)||l(p,e)}var g="case do else for if switch while struct enum union",v="struct enum union";function y(e,t){if(!t.startOfLine)return!1;for(var r,n=null;r=e.peek();){if("\\"==r&&e.match(/^.$/)){n=y;break}if("/"==r&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function b(e,t){return"type"==t.prevToken&&"type"}function w(e){return!(!e||e.length<2||"_"!=e[0]||"_"!=e[1]&&e[1]===e[1].toLowerCase())}function x(e){return e.eatWhile(/[\w\.']/),"number"}function k(e,t){if(e.backUp(1),e.match(/^(?:R|u8R|uR|UR|LR)/)){var r=e.match(/^"([^\s\\()]{0,16})\(/);return!!r&&(t.cpp11RawStringDelim=r[1],t.tokenize=S,S(e,t))}return e.match(/^(?:u8|u|U|L)/)?!!e.match(/^["']/,!1)&&"string":(e.next(),!1)}function _(e){var t=/(\w+)::~?(\w+)$/.exec(e);return t&&t[1]==t[2]}function C(e,t){for(var r;null!=(r=e.next());)if('"'==r&&!e.eat('"')){t.tokenize=null;break}return"string"}function S(e,t){var r=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+r+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function M(t,r){"string"==typeof t&&(t=[t]);var n=[];function i(e){if(e)for(var t in e)e.hasOwnProperty(t)&&n.push(t)}i(r.keywords),i(r.types),i(r.builtin),i(r.atoms),n.length&&(r.helperType=t[0],e.registerHelper("hintWords",t[0],n));for(var o=0;o<t.length;++o)e.defineMIME(t[o],r)}function T(e,t){for(var r=!1;!e.eol();){if(!r&&e.match('"""')){t.tokenize=null;break}r="\\"==e.next()&&!r}return"string"}function L(e){return function(t,r){for(var n;n=t.next();){if("*"==n&&t.eat("/")){if(1==e){r.tokenize=null;break}return r.tokenize=L(e-1),r.tokenize(t,r)}if("/"==n&&t.eat("*"))return r.tokenize=L(e+1),r.tokenize(t,r)}return"comment"}}function N(e){return function(t,r){for(var n,i=!1,o=!1;!t.eol();){if(!e&&!i&&t.match('"')){o=!0;break}if(e&&t.match('"""')){o=!0;break}n=t.next(),!i&&"$"==n&&t.match("{")&&t.skipTo("}"),i=!i&&"\\"==n&&!e}return!o&&e||(r.tokenize=null),"string"}}M(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:a(s),types:h,blockKeywords:a(g),defKeywords:a(v),typeFirstDefinitions:!0,atoms:a("NULL true false"),isReservedIdentifier:w,hooks:{"#":y,"*":b},modeProps:{fold:["brace","include"]}}),M(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:a(s+" "+c),types:h,blockKeywords:a(g+" class try catch"),defKeywords:a(v+" class namespace"),typeFirstDefinitions:!0,atoms:a("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:w,hooks:{"#":y,"*":b,u:k,U:k,L:k,R:k,0:x,1:x,2:x,3:x,4:x,5:x,6:x,7:x,8:x,9:x,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&_(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),M("text/x-java",{name:"clike",keywords:a("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:a("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:a("catch class do else finally for if switch try while"),defKeywords:a("class interface enum @interface"),typeFirstDefinitions:!0,atoms:a("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")},'"':function(e,t){return!!e.match(/""$/)&&(t.tokenize=T,t.tokenize(e,t))}},modeProps:{fold:["brace","import"]}}),M("text/x-csharp",{name:"clike",keywords:a("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:a("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:a("catch class do else finally for foreach if struct switch try while"),defKeywords:a("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=C,C(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),M("text/x-scala",{name:"clike",keywords:a("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:a("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:a("catch class enum do else finally for forSome if match switch try while"),defKeywords:a("class enum def object package trait type val var"),atoms:a("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=T,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(e,r){var n=r.context;return!("}"!=n.type||!n.align||!e.eat(">"))&&(r.context=new t(n.indented,n.column,n.type,n.info,null,n.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=L(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),M("text/x-kotlin",{name:"clike",keywords:a("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:a("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:a("catch class do else finally for if where try while enum"),defKeywords:a("class val var object interface fun"),atoms:a("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){return t.tokenize=N(e.match('""')),t.tokenize(e,t)},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=L(1),t.tokenize(e,t))},indent:function(e,t,r,n){var i=r&&r.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=r?"operator"==e.prevToken&&"}"!=r&&"}"!=e.context.type||"variable"==e.prevToken&&"."==i||("}"==e.prevToken||")"==e.prevToken)&&"."==i?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(r||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),M(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:a("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:a("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:a("for while do if else struct"),builtin:a("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:a("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":y},modeProps:{fold:["brace","include"]}}),M("text/x-nesc",{name:"clike",keywords:a(s+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:h,blockKeywords:a(g),atoms:a("null true false"),hooks:{"#":y},modeProps:{fold:["brace","include"]}}),M("text/x-objectivec",{name:"clike",keywords:a(s+" "+u),types:m,builtin:a(d),blockKeywords:a(g+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:a(v+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:a("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:w,hooks:{"#":y,"*":b},modeProps:{fold:["brace","include"]}}),M("text/x-objectivec++",{name:"clike",keywords:a(s+" "+u+" "+c),types:m,builtin:a(d),blockKeywords:a(g+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:a(v+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:a("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:w,hooks:{"#":y,"*":b,u:k,U:k,L:k,R:k,0:x,1:x,2:x,3:x,4:x,5:x,6:x,7:x,8:x,9:x,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&_(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),M("text/x-squirrel",{name:"clike",keywords:a("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:h,blockKeywords:a("case catch class else for foreach if switch try while"),defKeywords:a("function local class"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"#":y},modeProps:{fold:["brace","include"]}});var A=null;function O(e){return function(t,r){for(var n,i=!1,o=!1;!t.eol();){if(!i&&t.match('"')&&("single"==e||t.match('""'))){o=!0;break}if(!i&&t.match("``")){A=O(e),o=!0;break}n=t.next(),i="single"==e&&!i&&"\\"==n}return o&&(r.tokenize=null),"string"}}M("text/x-ceylon",{name:"clike",keywords:a("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:a("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:a("class dynamic function interface module object package value"),builtin:a("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:a("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=O(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!A||!e.match("`"))&&(t.tokenize=A,A=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,r){if(("variable"==r||"type"==r)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}(r(631))},629:(e,t,r)=>{!function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,l=r.tokenHooks,s=r.documentTypes||{},c=r.mediaTypes||{},u=r.mediaFeatures||{},d=r.mediaValueKeywords||{},f=r.propertyKeywords||{},p=r.nonStandardPropertyKeywords||{},h=r.fontProperties||{},m=r.counterDescriptors||{},g=r.colorKeywords||{},v=r.valueKeywords||{},y=r.allowNested,b=r.lineComment,w=!0===r.supportsAtComponent,x=!1!==t.highlightNonStandardPropertyKeywords;function k(e,t){return i=t,e}function _(e,t){var r=e.next();if(l[r]){var n=l[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),k("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?k(null,"compare"):'"'==r||"'"==r?(t.tokenize=C(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),k("atom","hash")):"!"==r?(e.match(/^\s*\w*/),k("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),k("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?k(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?k("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?k(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=S),k("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),k("property","word")):k(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),k("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?k("variable-2","variable-definition"):k("variable-2","variable")):e.match(/^\w+-/)?k("meta","meta"):void 0}function C(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),k("string","string")}}function S(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=C(")"),k(null,"(")}function M(e,t,r){this.type=e,this.indent=t,this.prev=r}function T(e,t,r,n){return e.context=new M(r,t.indentation()+(!1===n?0:a),e.context),r}function L(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function N(e,t,r){return z[r.context.type](e,t,r)}function A(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return N(e,t,r)}function O(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":g.hasOwnProperty(t)?"keyword":"variable"}var z={top:function(e,t,r){if("{"==e)return T(r,t,"block");if("}"==e&&r.context.prev)return L(r);if(w&&/@component/i.test(e))return T(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return T(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return T(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return T(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return T(r,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return T(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return f.hasOwnProperty(n)?(o="property","maybeprop"):p.hasOwnProperty(n)?(o=x?"string-2":"property","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?z.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?T(r,t,"prop"):N(e,t,r)},prop:function(e,t,r){if(";"==e)return L(r);if("{"==e&&y)return T(r,t,"propBlock");if("}"==e||"{"==e)return A(e,t,r);if("("==e)return T(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)O(t);else if("interpolation"==e)return T(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?L(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?A(e,t,r):")"==e?L(r):"("==e?T(r,t,"parens"):"interpolation"==e?T(r,t,"interpolation"):("word"==e&&O(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(o="variable-3",r.context.type):N(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&s.hasOwnProperty(t.current())?(o="tag",r.context.type):z.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return T(r,t,"atBlock_parens");if("}"==e||";"==e)return A(e,t,r);if("{"==e)return L(r)&&T(r,t,y?"block":"top");if("interpolation"==e)return T(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":c.hasOwnProperty(n)?"attribute":u.hasOwnProperty(n)?"property":d.hasOwnProperty(n)?"keyword":f.hasOwnProperty(n)?"property":p.hasOwnProperty(n)?x?"string-2":"property":v.hasOwnProperty(n)?"atom":g.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?A(e,t,r):"{"==e?L(r)&&T(r,t,y?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?L(r):"{"==e||"}"==e?A(e,t,r,2):z.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?T(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):N(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,L(r)):"word"==e?(o="@font-face"==r.stateArg&&!h.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?T(r,t,"top"):N(e,t,r)},at:function(e,t,r){return";"==e?L(r):"{"==e||"}"==e?A(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?L(r):"{"==e||";"==e?A(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new M(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||_)(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,"comment"!=i&&(t.state=z[t.state](i,e,t)),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:b,fold:"brace"}}));var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],l=t(a),s=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],c=t(s),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],d=t(u),f=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],p=t(f),h=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),m=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),g=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(g),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),w=r.concat(i).concat(a).concat(s).concat(u).concat(f).concat(g).concat(y);function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:p,fontProperties:h,counterDescriptors:m,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:p,colorKeywords:v,valueKeywords:b,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:p,colorKeywords:v,valueKeywords:b,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:l,propertyKeywords:d,nonStandardPropertyKeywords:p,fontProperties:h,counterDescriptors:m,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})}(r(631))},531:(e,t,r)=>{!function(e){"use strict";var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};function r(e,t,r){var n=e.current(),i=n.search(t);return i>-1?e.backUp(n.length-i):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}var n={};function i(e){var t=n[e];return t||(n[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}function o(e,t){var r=e.match(i(t));return r?/^\s*(.*?)\s*$/.exec(r[2])[1]:""}function a(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function l(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),i=e[r],o=i.length-1;o>=0;o--)n.unshift(i[o])}function s(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(!n[0]||n[1].test(o(t,n[0])))return n[2]}}e.defineMode("htmlmixed",(function(n,i){var o=e.getMode(n,{name:"xml",htmlMode:!0,multilineTagIndentFactor:i.multilineTagIndentFactor,multilineTagIndentPastTag:i.multilineTagIndentPastTag,allowMissingTagName:i.allowMissingTagName}),c={},u=i&&i.tags,d=i&&i.scriptTypes;if(l(t,c),u&&l(u,c),d)for(var f=d.length-1;f>=0;f--)c.script.unshift(["type",d[f].matches,d[f].mode]);function p(t,i){var l,u=o.token(t,i.htmlState),d=/\btag\b/.test(u);if(d&&!/[<>\s\/]/.test(t.current())&&(l=i.htmlState.tagName&&i.htmlState.tagName.toLowerCase())&&c.hasOwnProperty(l))i.inTag=l+" ";else if(i.inTag&&d&&/>$/.test(t.current())){var f=/^([\S]+) (.*)/.exec(i.inTag);i.inTag=null;var h=">"==t.current()&&s(c[f[1]],f[2]),m=e.getMode(n,h),g=a(f[1],!0),v=a(f[1],!1);i.token=function(e,t){return e.match(g,!1)?(t.token=p,t.localState=t.localMode=null,null):r(e,v,t.localMode.token(e,t.localState))},i.localMode=m,i.localState=e.startState(m,o.indent(i.htmlState,"",""))}else i.inTag&&(i.inTag+=t.current(),t.eol()&&(i.inTag+=" "));return u}return{startState:function(){return{token:p,inTag:null,localMode:null,localState:null,htmlState:e.startState(o)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(o,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?o.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||o}}}}),"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}(r(631),r(589),r(876),r(629))},876:(e,t,r)=>{!function(e){"use strict";e.defineMode("javascript",(function(t,r){var n,i,o=t.indentUnit,a=r.statementIndent,l=r.jsonld,s=r.json||l,c=!1!==r.trackScope,u=r.typescript,d=r.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),p=/[+\-*&%=<>!?|~^@]/,h=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function m(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function g(e,t,r){return n=e,i=r,t}function v(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=y(r),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return g("number","number");if("."==r&&e.match(".."))return g("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return g(r);if("="==r&&e.eat(">"))return g("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return g("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),g("number","number");if("/"==r)return e.eat("*")?(t.tokenize=b,b(e,t)):e.eat("/")?(e.skipToEnd(),g("comment","comment")):it(e,t,1)?(m(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),g("regexp","string-2")):(e.eat("="),g("operator","operator",e.current()));if("`"==r)return t.tokenize=w,w(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),g("meta","meta");if("#"==r&&e.eatWhile(d))return g("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),g("comment","comment");if(p.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?g("."):g("operator","operator",e.current());if(d.test(r)){e.eatWhile(d);var n=e.current();if("."!=t.lastType){if(f.propertyIsEnumerable(n)){var i=f[n];return g(i.type,i.style,n)}if("async"==n&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return g("async","keyword",n)}return g("variable","variable",n)}}function y(e){return function(t,r){var n,i=!1;if(l&&"@"==t.peek()&&t.match(h))return r.tokenize=v,g("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||i);)i=!i&&"\\"==n;return i||(r.tokenize=v),g("string","string")}}function b(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=v;break}n="*"==r}return g("comment","comment")}function w(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=v;break}n=!n&&"\\"==r}return g("quasi","string-2",e.current())}var x="([{}])";function k(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(u){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;a>=0;--a){var l=e.string.charAt(a),s=x.indexOf(l);if(s>=0&&s<3){if(!i){++a;break}if(0==--i){"("==l&&(o=!0);break}}else if(s>=3&&s<6)++i;else if(d.test(l))o=!0;else if(/["'\/`]/.test(l))for(;;--a){if(0==a)return;if(e.string.charAt(a-1)==l&&"\\"!=e.string.charAt(a-2)){a--;break}}else if(o&&!i){++a;break}}o&&!i&&(t.fatArrowAt=a)}}var _={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function C(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function S(e,t){if(!c)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}function M(e,t,r,n,i){var o=e.cc;for(T.state=e,T.stream=i,T.marked=null,T.cc=o,T.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():s?K:j)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return T.marked?T.marked:"variable"==r&&S(e,n)?"variable-2":t}}var T={state:null,column:null,marked:null,cc:null};function L(){for(var e=arguments.length-1;e>=0;e--)T.cc.push(arguments[e])}function N(){return L.apply(null,arguments),!0}function A(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function O(e){var t=T.state;if(T.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=z(e,t.context);if(null!=n)return void(t.context=n)}else if(!A(e,t.localVars))return void(t.localVars=new I(e,t.localVars));r.globalVars&&!A(e,t.globalVars)&&(t.globalVars=new I(e,t.globalVars))}}function z(e,t){if(t){if(t.block){var r=z(e,t.prev);return r?r==t.prev?t:new E(r,t.vars,!0):null}return A(e,t.vars)?t:new E(t.prev,new I(e,t.vars),!1)}return null}function D(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function E(e,t,r){this.prev=e,this.vars=t,this.block=r}function I(e,t){this.name=e,this.next=t}var P=new I("this",new I("arguments",null));function W(){T.state.context=new E(T.state.context,T.state.localVars,!1),T.state.localVars=P}function q(){T.state.context=new E(T.state.context,T.state.localVars,!0),T.state.localVars=null}function F(){T.state.localVars=T.state.context.vars,T.state.context=T.state.context.prev}function H(e,t){var r=function(){var r=T.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new C(n,T.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function R(){var e=T.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function B(e){function t(r){return r==e?N():";"==e||"}"==r||")"==r||"]"==r?L():N(t)}return t}function j(e,t){return"var"==e?N(H("vardef",t),Le,B(";"),R):"keyword a"==e?N(H("form"),$,j,R):"keyword b"==e?N(H("form"),j,R):"keyword d"==e?T.stream.match(/^\s*$/,!1)?N():N(H("stat"),X,B(";"),R):"debugger"==e?N(B(";")):"{"==e?N(H("}"),q,fe,R,F):";"==e?N():"if"==e?("else"==T.state.lexical.info&&T.state.cc[T.state.cc.length-1]==R&&T.state.cc.pop()(),N(H("form"),$,j,R,Ee)):"function"==e?N(qe):"for"==e?N(H("form"),q,Ie,j,F,R):"class"==e||u&&"interface"==t?(T.marked="keyword",N(H("form","class"==e?e:t),je,R)):"variable"==e?u&&"declare"==t?(T.marked="keyword",N(j)):u&&("module"==t||"enum"==t||"type"==t)&&T.stream.match(/^\s*\w/,!1)?(T.marked="keyword","enum"==t?N(tt):"type"==t?N(He,B("operator"),ve,B(";")):N(H("form"),Ne,B("{"),H("}"),fe,R,R)):u&&"namespace"==t?(T.marked="keyword",N(H("form"),K,j,R)):u&&"abstract"==t?(T.marked="keyword",N(j)):N(H("stat"),oe):"switch"==e?N(H("form"),$,B("{"),H("}","switch"),q,fe,R,R,F):"case"==e?N(K,B(":")):"default"==e?N(B(":")):"catch"==e?N(H("form"),W,U,j,R,F):"export"==e?N(H("stat"),$e,R):"import"==e?N(H("stat"),Xe,R):"async"==e?N(j):"@"==t?N(K,j):L(H("stat"),K,B(";"),R)}function U(e){if("("==e)return N(Re,B(")"))}function K(e,t){return G(e,t,!1)}function V(e,t){return G(e,t,!0)}function $(e){return"("!=e?L():N(H(")"),X,B(")"),R)}function G(e,t,r){if(T.state.fatArrowAt==T.stream.start){var n=r?te:ee;if("("==e)return N(W,H(")"),ue(Re,")"),R,B("=>"),n,F);if("variable"==e)return L(W,Ne,B("=>"),n,F)}var i=r?Z:Y;return _.hasOwnProperty(e)?N(i):"function"==e?N(qe,i):"class"==e||u&&"interface"==t?(T.marked="keyword",N(H("form"),Be,R)):"keyword c"==e||"async"==e?N(r?V:K):"("==e?N(H(")"),X,B(")"),R,i):"operator"==e||"spread"==e?N(r?V:K):"["==e?N(H("]"),et,R,i):"{"==e?de(le,"}",null,i):"quasi"==e?L(Q,i):"new"==e?N(re(r)):N()}function X(e){return e.match(/[;\}\)\],]/)?L():L(K)}function Y(e,t){return","==e?N(X):Z(e,t,!1)}function Z(e,t,r){var n=0==r?Y:Z,i=0==r?K:V;return"=>"==e?N(W,r?te:ee,F):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?N(n):u&&"<"==t&&T.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?N(H(">"),ue(ve,">"),R,n):"?"==t?N(K,B(":"),i):N(i):"quasi"==e?L(Q,n):";"!=e?"("==e?de(V,")","call",n):"."==e?N(ae,n):"["==e?N(H("]"),X,B("]"),R,n):u&&"as"==t?(T.marked="keyword",N(ve,n)):"regexp"==e?(T.state.lastType=T.marked="operator",T.stream.backUp(T.stream.pos-T.stream.start-1),N(i)):void 0:void 0}function Q(e,t){return"quasi"!=e?L():"${"!=t.slice(t.length-2)?N(Q):N(X,J)}function J(e){if("}"==e)return T.marked="string-2",T.state.tokenize=w,N(Q)}function ee(e){return k(T.stream,T.state),L("{"==e?j:K)}function te(e){return k(T.stream,T.state),L("{"==e?j:V)}function re(e){return function(t){return"."==t?N(e?ie:ne):"variable"==t&&u?N(Se,e?Z:Y):L(e?V:K)}}function ne(e,t){if("target"==t)return T.marked="keyword",N(Y)}function ie(e,t){if("target"==t)return T.marked="keyword",N(Z)}function oe(e){return":"==e?N(R,j):L(Y,B(";"),R)}function ae(e){if("variable"==e)return T.marked="property",N()}function le(e,t){return"async"==e?(T.marked="property",N(le)):"variable"==e||"keyword"==T.style?(T.marked="property","get"==t||"set"==t?N(se):(u&&T.state.fatArrowAt==T.stream.start&&(r=T.stream.match(/^\s*:\s*/,!1))&&(T.state.fatArrowAt=T.stream.pos+r[0].length),N(ce))):"number"==e||"string"==e?(T.marked=l?"property":T.style+" property",N(ce)):"jsonld-keyword"==e?N(ce):u&&D(t)?(T.marked="keyword",N(le)):"["==e?N(K,pe,B("]"),ce):"spread"==e?N(V,ce):"*"==t?(T.marked="keyword",N(le)):":"==e?L(ce):void 0;var r}function se(e){return"variable"!=e?L(ce):(T.marked="property",N(qe))}function ce(e){return":"==e?N(V):"("==e?L(qe):void 0}function ue(e,t,r){function n(i,o){if(r?r.indexOf(i)>-1:","==i){var a=T.state.lexical;return"call"==a.info&&(a.pos=(a.pos||0)+1),N((function(r,n){return r==t||n==t?L():L(e)}),n)}return i==t||o==t?N():r&&r.indexOf(";")>-1?L(e):N(B(t))}return function(r,i){return r==t||i==t?N():L(e,n)}}function de(e,t,r){for(var n=3;n<arguments.length;n++)T.cc.push(arguments[n]);return N(H(t,r),ue(e,t),R)}function fe(e){return"}"==e?N():L(j,fe)}function pe(e,t){if(u){if(":"==e)return N(ve);if("?"==t)return N(pe)}}function he(e,t){if(u&&(":"==e||"in"==t))return N(ve)}function me(e){if(u&&":"==e)return T.stream.match(/^\s*\w+\s+is\b/,!1)?N(K,ge,ve):N(ve)}function ge(e,t){if("is"==t)return T.marked="keyword",N()}function ve(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(T.marked="keyword",N("typeof"==t?V:ve)):"variable"==e||"void"==t?(T.marked="type",N(Ce)):"|"==t||"&"==t?N(ve):"string"==e||"number"==e||"atom"==e?N(Ce):"["==e?N(H("]"),ue(ve,"]",","),R,Ce):"{"==e?N(H("}"),be,R,Ce):"("==e?N(ue(_e,")"),ye,Ce):"<"==e?N(ue(ve,">"),ve):"quasi"==e?L(xe,Ce):void 0}function ye(e){if("=>"==e)return N(ve)}function be(e){return e.match(/[\}\)\]]/)?N():","==e||";"==e?N(be):L(we,be)}function we(e,t){return"variable"==e||"keyword"==T.style?(T.marked="property",N(we)):"?"==t||"number"==e||"string"==e?N(we):":"==e?N(ve):"["==e?N(B("variable"),he,B("]"),we):"("==e?L(Fe,we):e.match(/[;\}\)\],]/)?void 0:N()}function xe(e,t){return"quasi"!=e?L():"${"!=t.slice(t.length-2)?N(xe):N(ve,ke)}function ke(e){if("}"==e)return T.marked="string-2",T.state.tokenize=w,N(xe)}function _e(e,t){return"variable"==e&&T.stream.match(/^\s*[?:]/,!1)||"?"==t?N(_e):":"==e?N(ve):"spread"==e?N(_e):L(ve)}function Ce(e,t){return"<"==t?N(H(">"),ue(ve,">"),R,Ce):"|"==t||"."==e||"&"==t?N(ve):"["==e?N(ve,B("]"),Ce):"extends"==t||"implements"==t?(T.marked="keyword",N(ve)):"?"==t?N(ve,B(":"),ve):void 0}function Se(e,t){if("<"==t)return N(H(">"),ue(ve,">"),R,Ce)}function Me(){return L(ve,Te)}function Te(e,t){if("="==t)return N(ve)}function Le(e,t){return"enum"==t?(T.marked="keyword",N(tt)):L(Ne,pe,ze,De)}function Ne(e,t){return u&&D(t)?(T.marked="keyword",N(Ne)):"variable"==e?(O(t),N()):"spread"==e?N(Ne):"["==e?de(Oe,"]"):"{"==e?de(Ae,"}"):void 0}function Ae(e,t){return"variable"!=e||T.stream.match(/^\s*:/,!1)?("variable"==e&&(T.marked="property"),"spread"==e?N(Ne):"}"==e?L():"["==e?N(K,B("]"),B(":"),Ae):N(B(":"),Ne,ze)):(O(t),N(ze))}function Oe(){return L(Ne,ze)}function ze(e,t){if("="==t)return N(V)}function De(e){if(","==e)return N(Le)}function Ee(e,t){if("keyword b"==e&&"else"==t)return N(H("form","else"),j,R)}function Ie(e,t){return"await"==t?N(Ie):"("==e?N(H(")"),Pe,R):void 0}function Pe(e){return"var"==e?N(Le,We):"variable"==e?N(We):L(We)}function We(e,t){return")"==e?N():";"==e?N(We):"in"==t||"of"==t?(T.marked="keyword",N(K,We)):L(K,We)}function qe(e,t){return"*"==t?(T.marked="keyword",N(qe)):"variable"==e?(O(t),N(qe)):"("==e?N(W,H(")"),ue(Re,")"),R,me,j,F):u&&"<"==t?N(H(">"),ue(Me,">"),R,qe):void 0}function Fe(e,t){return"*"==t?(T.marked="keyword",N(Fe)):"variable"==e?(O(t),N(Fe)):"("==e?N(W,H(")"),ue(Re,")"),R,me,F):u&&"<"==t?N(H(">"),ue(Me,">"),R,Fe):void 0}function He(e,t){return"keyword"==e||"variable"==e?(T.marked="type",N(He)):"<"==t?N(H(">"),ue(Me,">"),R):void 0}function Re(e,t){return"@"==t&&N(K,Re),"spread"==e?N(Re):u&&D(t)?(T.marked="keyword",N(Re)):u&&"this"==e?N(pe,ze):L(Ne,pe,ze)}function Be(e,t){return"variable"==e?je(e,t):Ue(e,t)}function je(e,t){if("variable"==e)return O(t),N(Ue)}function Ue(e,t){return"<"==t?N(H(">"),ue(Me,">"),R,Ue):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(T.marked="keyword"),N(u?ve:K,Ue)):"{"==e?N(H("}"),Ke,R):void 0}function Ke(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&D(t))&&T.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(T.marked="keyword",N(Ke)):"variable"==e||"keyword"==T.style?(T.marked="property",N(Ve,Ke)):"number"==e||"string"==e?N(Ve,Ke):"["==e?N(K,pe,B("]"),Ve,Ke):"*"==t?(T.marked="keyword",N(Ke)):u&&"("==e?L(Fe,Ke):";"==e||","==e?N(Ke):"}"==e?N():"@"==t?N(K,Ke):void 0}function Ve(e,t){if("!"==t)return N(Ve);if("?"==t)return N(Ve);if(":"==e)return N(ve,ze);if("="==t)return N(V);var r=T.state.lexical.prev;return L(r&&"interface"==r.info?Fe:qe)}function $e(e,t){return"*"==t?(T.marked="keyword",N(Je,B(";"))):"default"==t?(T.marked="keyword",N(K,B(";"))):"{"==e?N(ue(Ge,"}"),Je,B(";")):L(j)}function Ge(e,t){return"as"==t?(T.marked="keyword",N(B("variable"))):"variable"==e?L(V,Ge):void 0}function Xe(e){return"string"==e?N():"("==e?L(K):"."==e?L(Y):L(Ye,Ze,Je)}function Ye(e,t){return"{"==e?de(Ye,"}"):("variable"==e&&O(t),"*"==t&&(T.marked="keyword"),N(Qe))}function Ze(e){if(","==e)return N(Ye,Ze)}function Qe(e,t){if("as"==t)return T.marked="keyword",N(Ye)}function Je(e,t){if("from"==t)return T.marked="keyword",N(K)}function et(e){return"]"==e?N():L(ue(V,"]"))}function tt(){return L(H("form"),Ne,B("{"),H("}"),ue(rt,"}"),R,R)}function rt(){return L(Ne,ze)}function nt(e,t){return"operator"==e.lastType||","==e.lastType||p.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function it(e,t,r){return t.tokenize==v&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return W.lex=q.lex=!0,F.lex=!0,R.lex=!0,{startState:function(e){var t={tokenize:v,lastType:"sof",cc:[],lexical:new C((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new E(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),k(e,t)),t.tokenize!=b&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",M(t,r,n,i,e))},indent:function(t,n){if(t.tokenize==b||t.tokenize==w)return e.Pass;if(t.tokenize!=v)return 0;var i,l=n&&n.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var u=t.cc[c];if(u==R)s=s.prev;else if(u!=Ee&&u!=F)break}for(;("stat"==s.type||"form"==s.type)&&("}"==l||(i=t.cc[t.cc.length-1])&&(i==Y||i==Z)&&!/^[,\.=+\-*:?[\(]/.test(n));)s=s.prev;a&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var d=s.type,f=l==d;return"vardef"==d?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==d&&"{"==l?s.indented:"form"==d?s.indented+o:"stat"==d?s.indented+(nt(t,n)?a||o:0):"switch"!=s.info||f||0==r.doubleIndentSwitch?s.align?s.column+(f?0:1):s.indented+(f?0:o):s.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:s?null:"/*",blockCommentEnd:s?null:"*/",blockCommentContinue:s?null:" * ",lineComment:s?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:s?"json":"javascript",jsonldMode:l,jsonMode:s,expressionAllowed:it,skipExpression:function(t){M(t,"atom","atom","true",new e.StringStream("",2,null))}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}(r(631))},959:(e,t,r)=>{!function(e){"use strict";function t(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function r(e,t,i){return 0==e.length?n(t):function(o,a){for(var l=e[0],s=0;s<l.length;s++)if(o.match(l[s][0]))return a.tokenize=r(e.slice(1),t),l[s][1];return a.tokenize=n(t,i),"string"}}function n(e,t){return function(r,n){return i(r,n,e,t)}}function i(e,t,n,i){if(!1!==i&&e.match("${",!1)||e.match("{$",!1))return t.tokenize=null,"string";if(!1!==i&&e.match(/^\$[a-zA-Z_][a-zA-Z0-9_]*/))return e.match("[",!1)&&(t.tokenize=r([[["[",null]],[[/\d[\w\.]*/,"number"],[/\$[a-zA-Z_][a-zA-Z0-9_]*/,"variable-2"],[/[\w\$]+/,"variable"]],[["]",null]]],n,i)),e.match(/^->\w/,!1)&&(t.tokenize=r([[["->",null]],[[/[\w]+/,"variable"]]],n,i)),"variable-2";for(var o=!1;!e.eol()&&(o||!1===i||!e.match("{$",!1)&&!e.match(/^(\$[a-zA-Z_][a-zA-Z0-9_]*|\$\{)/,!1));){if(!o&&e.match(n)){t.tokenize=null,t.tokStack.pop(),t.tokStack.pop();break}o="\\"==e.next()&&!o}return"string"}var o="abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile enum extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent yield insteadof finally readonly match",a="true false null TRUE FALSE NULL __CLASS__ __DIR__ __FILE__ __LINE__ __METHOD__ __FUNCTION__ __NAMESPACE__ __TRAIT__",l="func_num_args func_get_arg func_get_args strlen strcmp strncmp strcasecmp strncasecmp each error_reporting define defined trigger_error user_error set_error_handler restore_error_handler get_declared_classes get_loaded_extensions extension_loaded get_extension_funcs debug_backtrace constant bin2hex hex2bin sleep usleep time mktime gmmktime strftime gmstrftime strtotime date gmdate getdate localtime checkdate flush wordwrap htmlspecialchars htmlentities html_entity_decode md5 md5_file crc32 getimagesize image_type_to_mime_type phpinfo phpversion phpcredits strnatcmp strnatcasecmp substr_count strspn strcspn strtok strtoupper strtolower strpos strrpos strrev hebrev hebrevc nl2br basename dirname pathinfo stripslashes stripcslashes strstr stristr strrchr str_shuffle str_word_count strcoll substr substr_replace quotemeta ucfirst ucwords strtr addslashes addcslashes rtrim str_replace str_repeat count_chars chunk_split trim ltrim strip_tags similar_text explode implode setlocale localeconv parse_str str_pad chop strchr sprintf printf vprintf vsprintf sscanf fscanf parse_url urlencode urldecode rawurlencode rawurldecode readlink linkinfo link unlink exec system escapeshellcmd escapeshellarg passthru shell_exec proc_open proc_close rand srand getrandmax mt_rand mt_srand mt_getrandmax base64_decode base64_encode abs ceil floor round is_finite is_nan is_infinite bindec hexdec octdec decbin decoct dechex base_convert number_format fmod ip2long long2ip getenv putenv getopt microtime gettimeofday getrusage uniqid quoted_printable_decode set_time_limit get_cfg_var magic_quotes_runtime set_magic_quotes_runtime get_magic_quotes_gpc get_magic_quotes_runtime import_request_variables error_log serialize unserialize memory_get_usage memory_get_peak_usage var_dump var_export debug_zval_dump print_r highlight_file show_source highlight_string ini_get ini_get_all ini_set ini_alter ini_restore get_include_path set_include_path restore_include_path setcookie header headers_sent connection_aborted connection_status ignore_user_abort parse_ini_file is_uploaded_file move_uploaded_file intval floatval doubleval strval gettype settype is_null is_resource is_bool is_long is_float is_int is_integer is_double is_real is_numeric is_string is_array is_object is_scalar ereg ereg_replace eregi eregi_replace split spliti join sql_regcase dl pclose popen readfile rewind rmdir umask fclose feof fgetc fgets fgetss fread fopen fpassthru ftruncate fstat fseek ftell fflush fwrite fputs mkdir rename copy tempnam tmpfile file file_get_contents file_put_contents stream_select stream_context_create stream_context_set_params stream_context_set_option stream_context_get_options stream_filter_prepend stream_filter_append fgetcsv flock get_meta_tags stream_set_write_buffer set_file_buffer set_socket_blocking stream_set_blocking socket_set_blocking stream_get_meta_data stream_register_wrapper stream_wrapper_register stream_set_timeout socket_set_timeout socket_get_status realpath fnmatch fsockopen pfsockopen pack unpack get_browser crypt opendir closedir chdir getcwd rewinddir readdir dir glob fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype file_exists is_writable is_writeable is_readable is_executable is_file is_dir is_link stat lstat chown touch clearstatcache mail ob_start ob_flush ob_clean ob_end_flush ob_end_clean ob_get_flush ob_get_clean ob_get_length ob_get_level ob_get_status ob_get_contents ob_implicit_flush ob_list_handlers ksort krsort natsort natcasesort asort arsort sort rsort usort uasort uksort shuffle array_walk count end prev next reset current key min max in_array array_search extract compact array_fill range array_multisort array_push array_pop array_shift array_unshift array_splice array_slice array_merge array_merge_recursive array_keys array_values array_count_values array_reverse array_reduce array_pad array_flip array_change_key_case array_rand array_unique array_intersect array_intersect_assoc array_diff array_diff_assoc array_sum array_filter array_map array_chunk array_key_exists array_intersect_key array_combine array_column pos sizeof key_exists assert assert_options version_compare ftok str_rot13 aggregate session_name session_module_name session_save_path session_id session_regenerate_id session_decode session_register session_unregister session_is_registered session_encode session_start session_destroy session_unset session_set_save_handler session_cache_limiter session_cache_expire session_set_cookie_params session_get_cookie_params session_write_close preg_match preg_match_all preg_replace preg_replace_callback preg_split preg_quote preg_grep overload ctype_alnum ctype_alpha ctype_cntrl ctype_digit ctype_lower ctype_graph ctype_print ctype_punct ctype_space ctype_upper ctype_xdigit virtual apache_request_headers apache_note apache_lookup_uri apache_child_terminate apache_setenv apache_response_headers apache_get_version getallheaders mysql_connect mysql_pconnect mysql_close mysql_select_db mysql_create_db mysql_drop_db mysql_query mysql_unbuffered_query mysql_db_query mysql_list_dbs mysql_list_tables mysql_list_fields mysql_list_processes mysql_error mysql_errno mysql_affected_rows mysql_insert_id mysql_result mysql_num_rows mysql_num_fields mysql_fetch_row mysql_fetch_array mysql_fetch_assoc mysql_fetch_object mysql_data_seek mysql_fetch_lengths mysql_fetch_field mysql_field_seek mysql_free_result mysql_field_name mysql_field_table mysql_field_len mysql_field_type mysql_field_flags mysql_escape_string mysql_real_escape_string mysql_stat mysql_thread_id mysql_client_encoding mysql_get_client_info mysql_get_host_info mysql_get_proto_info mysql_get_server_info mysql_info mysql mysql_fieldname mysql_fieldtable mysql_fieldlen mysql_fieldtype mysql_fieldflags mysql_selectdb mysql_createdb mysql_dropdb mysql_freeresult mysql_numfields mysql_numrows mysql_listdbs mysql_listtables mysql_listfields mysql_db_name mysql_dbname mysql_tablename mysql_table_name pg_connect pg_pconnect pg_close pg_connection_status pg_connection_busy pg_connection_reset pg_host pg_dbname pg_port pg_tty pg_options pg_ping pg_query pg_send_query pg_cancel_query pg_fetch_result pg_fetch_row pg_fetch_assoc pg_fetch_array pg_fetch_object pg_fetch_all pg_affected_rows pg_get_result pg_result_seek pg_result_status pg_free_result pg_last_oid pg_num_rows pg_num_fields pg_field_name pg_field_num pg_field_size pg_field_type pg_field_prtlen pg_field_is_null pg_get_notify pg_get_pid pg_result_error pg_last_error pg_last_notice pg_put_line pg_end_copy pg_copy_to pg_copy_from pg_trace pg_untrace pg_lo_create pg_lo_unlink pg_lo_open pg_lo_close pg_lo_read pg_lo_write pg_lo_read_all pg_lo_import pg_lo_export pg_lo_seek pg_lo_tell pg_escape_string pg_escape_bytea pg_unescape_bytea pg_client_encoding pg_set_client_encoding pg_meta_data pg_convert pg_insert pg_update pg_delete pg_select pg_exec pg_getlastoid pg_cmdtuples pg_errormessage pg_numrows pg_numfields pg_fieldname pg_fieldsize pg_fieldtype pg_fieldnum pg_fieldprtlen pg_fieldisnull pg_freeresult pg_result pg_loreadall pg_locreate pg_lounlink pg_loopen pg_loclose pg_loread pg_lowrite pg_loimport pg_loexport http_response_code get_declared_traits getimagesizefromstring socket_import_stream stream_set_chunk_size trait_exists header_register_callback class_uses session_status session_register_shutdown echo print global static exit array empty eval isset unset die include require include_once require_once json_decode json_encode json_last_error json_last_error_msg curl_close curl_copy_handle curl_errno curl_error curl_escape curl_exec curl_file_create curl_getinfo curl_init curl_multi_add_handle curl_multi_close curl_multi_exec curl_multi_getcontent curl_multi_info_read curl_multi_init curl_multi_remove_handle curl_multi_select curl_multi_setopt curl_multi_strerror curl_pause curl_reset curl_setopt_array curl_setopt curl_share_close curl_share_init curl_share_setopt curl_strerror curl_unescape curl_version mysqli_affected_rows mysqli_autocommit mysqli_change_user mysqli_character_set_name mysqli_close mysqli_commit mysqli_connect_errno mysqli_connect_error mysqli_connect mysqli_data_seek mysqli_debug mysqli_dump_debug_info mysqli_errno mysqli_error_list mysqli_error mysqli_fetch_all mysqli_fetch_array mysqli_fetch_assoc mysqli_fetch_field_direct mysqli_fetch_field mysqli_fetch_fields mysqli_fetch_lengths mysqli_fetch_object mysqli_fetch_row mysqli_field_count mysqli_field_seek mysqli_field_tell mysqli_free_result mysqli_get_charset mysqli_get_client_info mysqli_get_client_stats mysqli_get_client_version mysqli_get_connection_stats mysqli_get_host_info mysqli_get_proto_info mysqli_get_server_info mysqli_get_server_version mysqli_info mysqli_init mysqli_insert_id mysqli_kill mysqli_more_results mysqli_multi_query mysqli_next_result mysqli_num_fields mysqli_num_rows mysqli_options mysqli_ping mysqli_prepare mysqli_query mysqli_real_connect mysqli_real_escape_string mysqli_real_query mysqli_reap_async_query mysqli_refresh mysqli_rollback mysqli_select_db mysqli_set_charset mysqli_set_local_infile_default mysqli_set_local_infile_handler mysqli_sqlstate mysqli_ssl_set mysqli_stat mysqli_stmt_init mysqli_store_result mysqli_thread_id mysqli_thread_safe mysqli_use_result mysqli_warning_count";e.registerHelper("hintWords","php",[o,a,l].join(" ").split(" ")),e.registerHelper("wordChars","php",/[\w$]/);var s={name:"clike",helperType:"php",keywords:t(o),blockKeywords:t("catch do else elseif for foreach if switch try while finally"),defKeywords:t("class enum function interface namespace trait"),atoms:t(a),builtin:t(l),multiLineStrings:!0,hooks:{$:function(e){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,t){var r;if(r=e.match(/^<<\s*/)){var i=e.eat(/['"]/);e.eatWhile(/[\w\.]/);var o=e.current().slice(r[0].length+(i?2:1));if(i&&e.eat(i),o)return(t.tokStack||(t.tokStack=[])).push(o,0),t.tokenize=n(o,"'"!=i),"string"}return!1},"#":function(e){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"},"/":function(e){if(e.eat("/")){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"}return!1},'"':function(e,t){return(t.tokStack||(t.tokStack=[])).push('"',0),t.tokenize=n('"'),"string"},"{":function(e,t){return t.tokStack&&t.tokStack.length&&t.tokStack[t.tokStack.length-1]++,!1},"}":function(e,t){return t.tokStack&&t.tokStack.length>0&&!--t.tokStack[t.tokStack.length-1]&&(t.tokenize=n(t.tokStack[t.tokStack.length-2])),!1}}};e.defineMode("php",(function(t,r){var n=e.getMode(t,r&&r.htmlMode||"text/html"),i=e.getMode(t,s);function o(t,r){var o=r.curMode==i;if(t.sol()&&r.pending&&'"'!=r.pending&&"'"!=r.pending&&(r.pending=null),o)return o&&null==r.php.tokenize&&t.match("?>")?(r.curMode=n,r.curState=r.html,r.php.context.prev||(r.php=null),"meta"):i.token(t,r.curState);if(t.match(/^<\?\w*/))return r.curMode=i,r.php||(r.php=e.startState(i,n.indent(r.html,"",""))),r.curState=r.php,"meta";if('"'==r.pending||"'"==r.pending){for(;!t.eol()&&t.next()!=r.pending;);var a="string"}else r.pending&&t.pos<r.pending.end?(t.pos=r.pending.end,a=r.pending.style):a=n.token(t,r.curState);r.pending&&(r.pending=null);var l,s=t.current(),c=s.search(/<\?/);return-1!=c&&("string"==a&&(l=s.match(/[\'\"]$/))&&!/\?>/.test(s)?r.pending=l[0]:r.pending={end:t.pos,style:a},t.backUp(s.length-c)),a}return{startState:function(){var t=e.startState(n),o=r.startOpen?e.startState(i):null;return{html:t,php:o,curMode:r.startOpen?i:n,curState:r.startOpen?o:t,pending:null}},copyState:function(t){var r,o=t.html,a=e.copyState(n,o),l=t.php,s=l&&e.copyState(i,l);return r=t.curMode==n?a:s,{html:a,php:s,curMode:t.curMode,curState:r,pending:t.pending}},token:o,indent:function(e,t,r){return e.curMode!=i&&/^\s*<\//.test(t)||e.curMode==i&&/^\?>/.test(t)?n.indent(e.html,t,r):e.curMode.indent(e.curState,t,r)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}}),"htmlmixed","clike"),e.defineMIME("application/x-httpd-php","php"),e.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),e.defineMIME("text/x-php",s)}(r(631),r(531),r(762))},702:(e,t,r)=>{!function(e){"use strict";e.defineMode("twig:inner",(function(){var e=["and","as","autoescape","endautoescape","block","do","endblock","else","elseif","extends","for","endfor","embed","endembed","filter","endfilter","flush","from","if","endif","in","is","include","import","not","or","set","spaceless","endspaceless","with","endwith","trans","endtrans","blocktrans","endblocktrans","macro","endmacro","use","verbatim","endverbatim"],t=/^[+\-*&%=<>!?|~^]/,r=/^[:\[\(\{]/,n=["true","false","null","empty","defined","divisibleby","divisible by","even","odd","iterable","sameas","same as"],i=/^(\d[+\-\*\/])?\d+(\.\d+)?/;function o(o,a){var l=o.peek();if(a.incomment)return o.skipTo("#}")?(o.eatWhile(/\#|}/),a.incomment=!1):o.skipToEnd(),"comment";if(a.intag){if(a.operator){if(a.operator=!1,o.match(n))return"atom";if(o.match(i))return"number"}if(a.sign){if(a.sign=!1,o.match(n))return"atom";if(o.match(i))return"number"}if(a.instring)return l==a.instring&&(a.instring=!1),o.next(),"string";if("'"==l||'"'==l)return a.instring=l,o.next(),"string";if(o.match(a.intag+"}")||o.eat("-")&&o.match(a.intag+"}"))return a.intag=!1,"tag";if(o.match(t))return a.operator=!0,"operator";if(o.match(r))a.sign=!0;else if(o.eat(" ")||o.sol()){if(o.match(e))return"keyword";if(o.match(n))return"atom";if(o.match(i))return"number";o.sol()&&o.next()}else o.next();return"variable"}if(o.eat("{")){if(o.eat("#"))return a.incomment=!0,o.skipTo("#}")?(o.eatWhile(/\#|}/),a.incomment=!1):o.skipToEnd(),"comment";if(l=o.eat(/\{|%/))return a.intag=l,"{"==l&&(a.intag="}"),o.eat("-"),"tag"}o.next()}return e=new RegExp("(("+e.join(")|(")+"))\\b"),n=new RegExp("(("+n.join(")|(")+"))\\b"),{startState:function(){return{}},token:function(e,t){return o(e,t)}}})),e.defineMode("twig",(function(t,r){var n=e.getMode(t,"twig:inner");return r&&r.base?e.multiplexingMode(e.getMode(t,r.base),{open:/\{[{#%]/,close:/[}#%]\}/,mode:n,parseDelimiters:!0}):n})),e.defineMIME("text/x-twig","twig")}(r(631),r(93))},589:(e,t,r)=>{!function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",(function(n,i){var o,a,l=n.indentUnit,s={},c=i.htmlMode?t:r;for(var u in c)s[u]=c[u];for(var u in i)s[u]=i[u];function d(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(h("atom","]]>")):null:e.match("--")?r(h("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(m(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=h("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=f,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function f(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=d,o=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return o="equals",null;if("<"==r){t.tokenize=d,t.state=w,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(r)?(t.tokenize=p(r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function p(e){var t=function(t,r){for(;!t.eol();)if(t.next()==e){r.tokenize=f;break}return"string"};return t.isInAttribute=!0,t}function h(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=d;break}r.next()}return e}}function m(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=m(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=d;break}return r.tokenize=m(e-1),r.tokenize(t,r)}}return"meta"}}function g(e){return e&&e.toLowerCase()}function v(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function y(e){e.context&&(e.context=e.context.prev)}function b(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!s.contextGrabbers.hasOwnProperty(g(r))||!s.contextGrabbers[g(r)].hasOwnProperty(g(t)))return;y(e)}}function w(e,t,r){return"openTag"==e?(r.tagStart=t.column(),x):"closeTag"==e?k:w}function x(e,t,r){return"word"==e?(r.tagName=t.current(),a="tag",S):s.allowMissingTagName&&"endTag"==e?(a="tag bracket",S(e,t,r)):(a="error",x)}function k(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&s.implicitlyClosed.hasOwnProperty(g(r.context.tagName))&&y(r),r.context&&r.context.tagName==n||!1===s.matchClosing?(a="tag",_):(a="tag error",C)}return s.allowMissingTagName&&"endTag"==e?(a="tag bracket",_(e,t,r)):(a="error",C)}function _(e,t,r){return"endTag"!=e?(a="error",_):(y(r),w)}function C(e,t,r){return a="error",_(e,t,r)}function S(e,t,r){if("word"==e)return a="attribute",M;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(g(n))?b(r,n):(b(r,n),r.context=new v(r,n,i==r.indented)),w}return a="error",S}function M(e,t,r){return"equals"==e?T:(s.allowMissing||(a="error"),S(e,t,r))}function T(e,t,r){return"string"==e?L:"word"==e&&s.allowUnquoted?(a="string",S):(a="error",S(e,t,r))}function L(e,t,r){return"string"==e?L:S(e,t,r)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:w,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(a=null,t.state=t.state(o||r,e,t),a&&(r="error"==a?r+" error":a)),r},indent:function(t,r,n){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=f&&t.tokenize!=d)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==s.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!s.implicitlyClosed.hasOwnProperty(g(i.tagName)))break;i=i.prev}else if(o)for(;i;){var a=s.contextGrabbers[g(i.tagName)];if(!a||!a.hasOwnProperty(g(o[2])))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==T&&(e.state=S)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}(r(631))},789:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(645),i=r.n(n)()((function(e){return e[1]}));i.push([e.id,'.CodeMirror{color:#000;direction:ltr;font-family:monospace;height:300px}.CodeMirror-lines{padding:4px 0}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{padding:0 4px}.CodeMirror-gutter-filler,.CodeMirror-scrollbar-filler{background-color:#fff}.CodeMirror-gutters{background-color:#f7f7f7;border-right:1px solid #ddd;white-space:nowrap}.CodeMirror-linenumber{color:#999;min-width:20px;padding:0 3px 0 5px;text-align:right;white-space:nowrap}.CodeMirror-guttermarker{color:#000}.CodeMirror-guttermarker-subtle{color:#999}.CodeMirror-cursor{border-left:1px solid #000;border-right:none;width:0}.CodeMirror div.CodeMirror-secondarycursor{border-left:1px solid silver}.cm-fat-cursor .CodeMirror-cursor{background:#7e7;border:0!important;width:auto}.cm-fat-cursor div.CodeMirror-cursors{z-index:1}.cm-fat-cursor .CodeMirror-line::selection,.cm-fat-cursor .CodeMirror-line>span::selection,.cm-fat-cursor .CodeMirror-line>span>span::selection{background:transparent}.cm-fat-cursor .CodeMirror-line::-moz-selection,.cm-fat-cursor .CodeMirror-line>span::-moz-selection,.cm-fat-cursor .CodeMirror-line>span>span::-moz-selection{background:transparent}.cm-fat-cursor{caret-color:transparent}@-webkit-keyframes blink{50%{background-color:transparent}}@keyframes blink{50%{background-color:transparent}}.cm-tab{display:inline-block;text-decoration:inherit}.CodeMirror-rulers{bottom:0;left:0;overflow:hidden;position:absolute;right:0;top:-50px}.CodeMirror-ruler{border-left:1px solid #ccc;bottom:0;position:absolute;top:0}.cm-s-default .cm-header{color:blue}.cm-s-default .cm-quote{color:#090}.cm-negative{color:#d44}.cm-positive{color:#292}.cm-header,.cm-strong{font-weight:700}.cm-em{font-style:italic}.cm-link{text-decoration:underline}.cm-strikethrough{text-decoration:line-through}.cm-s-default .cm-keyword{color:#708}.cm-s-default .cm-atom{color:#219}.cm-s-default .cm-number{color:#164}.cm-s-default .cm-def{color:#00f}.cm-s-default .cm-variable-2{color:#05a}.cm-s-default .cm-type,.cm-s-default .cm-variable-3{color:#085}.cm-s-default .cm-comment{color:#a50}.cm-s-default .cm-string{color:#a11}.cm-s-default .cm-string-2{color:#f50}.cm-s-default .cm-meta,.cm-s-default .cm-qualifier{color:#555}.cm-s-default .cm-builtin{color:#30a}.cm-s-default .cm-bracket{color:#997}.cm-s-default .cm-tag{color:#170}.cm-s-default .cm-attribute{color:#00c}.cm-s-default .cm-hr{color:#999}.cm-s-default .cm-link{color:#00c}.cm-invalidchar,.cm-s-default .cm-error{color:red}.CodeMirror-composing{border-bottom:2px solid}div.CodeMirror span.CodeMirror-matchingbracket{color:#0b0}div.CodeMirror span.CodeMirror-nonmatchingbracket{color:#a22}.CodeMirror-matchingtag{background:rgba(255,150,0,.3)}.CodeMirror-activeline-background{background:#e8f2ff}.CodeMirror{background:#fff;overflow:hidden;position:relative}.CodeMirror-scroll{height:100%;margin-bottom:-50px;margin-right:-50px;outline:none;overflow:scroll!important;padding-bottom:50px;position:relative;z-index:0}.CodeMirror-sizer{border-right:50px solid transparent;position:relative}.CodeMirror-gutter-filler,.CodeMirror-hscrollbar,.CodeMirror-scrollbar-filler,.CodeMirror-vscrollbar{display:none;outline:none;position:absolute;z-index:6}.CodeMirror-vscrollbar{overflow-x:hidden;overflow-y:scroll;right:0;top:0}.CodeMirror-hscrollbar{bottom:0;left:0;overflow-x:scroll;overflow-y:hidden}.CodeMirror-scrollbar-filler{bottom:0;right:0}.CodeMirror-gutter-filler{bottom:0;left:0}.CodeMirror-gutters{left:0;min-height:100%;position:absolute;top:0;z-index:3}.CodeMirror-gutter{display:inline-block;height:100%;margin-bottom:-50px;vertical-align:top;white-space:normal}.CodeMirror-gutter-wrapper{background:none!important;border:none!important;position:absolute;z-index:4}.CodeMirror-gutter-background{bottom:0;position:absolute;top:0;z-index:4}.CodeMirror-gutter-elt{cursor:default;position:absolute;z-index:4}.CodeMirror-gutter-wrapper ::selection{background-color:transparent}.CodeMirror-gutter-wrapper ::-moz-selection{background-color:transparent}.CodeMirror-lines{cursor:text;min-height:1px}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{word-wrap:normal;-webkit-tap-highlight-color:transparent;background:transparent;border-radius:0;border-width:0;color:inherit;font-family:inherit;font-size:inherit;font-variant-ligatures:contextual;line-height:inherit;margin:0;overflow:visible;position:relative;white-space:pre;z-index:2}.CodeMirror-wrap pre.CodeMirror-line,.CodeMirror-wrap pre.CodeMirror-line-like{word-wrap:break-word;white-space:pre-wrap;word-break:normal}.CodeMirror-linebackground{bottom:0;left:0;position:absolute;right:0;top:0;z-index:0}.CodeMirror-linewidget{padding:.1px;position:relative;z-index:2}.CodeMirror-rtl pre{direction:rtl}.CodeMirror-code{outline:none}.CodeMirror-gutter,.CodeMirror-gutters,.CodeMirror-linenumber,.CodeMirror-scroll,.CodeMirror-sizer{box-sizing:content-box}.CodeMirror-measure{height:0;overflow:hidden;position:absolute;visibility:hidden;width:100%}.CodeMirror-cursor{pointer-events:none;position:absolute}.CodeMirror-measure pre{position:static}div.CodeMirror-cursors{position:relative;visibility:hidden;z-index:3}.CodeMirror-focused div.CodeMirror-cursors,div.CodeMirror-dragcursors{visibility:visible}.CodeMirror-selected{background:#d9d9d9}.CodeMirror-focused .CodeMirror-selected{background:#d7d4f0}.CodeMirror-crosshair{cursor:crosshair}.CodeMirror-line::selection,.CodeMirror-line>span::selection,.CodeMirror-line>span>span::selection{background:#d7d4f0}.CodeMirror-line::-moz-selection,.CodeMirror-line>span::-moz-selection,.CodeMirror-line>span>span::-moz-selection{background:#d7d4f0}.cm-searching{background-color:#ffa;background-color:rgba(255,255,0,.4)}.cm-force-border{padding-right:.1px}@media print{.CodeMirror div.CodeMirror-cursors{visibility:hidden}}.cm-tab-wrap-hack:after{content:""}span.CodeMirror-selectedtext{background:none}',""]);const o=i},845:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(645),i=r.n(n)()((function(e){return e[1]}));i.push([e.id,".cm-s-twilight.CodeMirror{background:#141414;color:#f7f7f7}.cm-s-twilight div.CodeMirror-selected{background:#323232}.cm-s-twilight .CodeMirror-line::selection,.cm-s-twilight .CodeMirror-line>span::selection,.cm-s-twilight .CodeMirror-line>span>span::selection{background:rgba(50,50,50,.99)}.cm-s-twilight .CodeMirror-line::-moz-selection,.cm-s-twilight .CodeMirror-line>span::-moz-selection,.cm-s-twilight .CodeMirror-line>span>span::-moz-selection{background:rgba(50,50,50,.99)}.cm-s-twilight .CodeMirror-gutters{background:#222;border-right:1px solid #aaa}.cm-s-twilight .CodeMirror-guttermarker{color:#fff}.cm-s-twilight .CodeMirror-guttermarker-subtle,.cm-s-twilight .CodeMirror-linenumber{color:#aaa}.cm-s-twilight .CodeMirror-cursor{border-left:1px solid #fff}.cm-s-twilight .cm-keyword{color:#f9ee98}.cm-s-twilight .cm-atom{color:#fc0}.cm-s-twilight .cm-number{color:#ca7841}.cm-s-twilight .cm-def{color:#8da6ce}.cm-s-twilight span.cm-def,.cm-s-twilight span.cm-tag,.cm-s-twilight span.cm-type,.cm-s-twilight span.cm-variable-2,.cm-s-twilight span.cm-variable-3{color:#607392}.cm-s-twilight .cm-operator{color:#cda869}.cm-s-twilight .cm-comment{color:#777;font-style:italic;font-weight:400}.cm-s-twilight .cm-string{color:#8f9d6a;font-style:italic}.cm-s-twilight .cm-string-2{color:#bd6b18}.cm-s-twilight .cm-meta{background-color:#141414;color:#f7f7f7}.cm-s-twilight .cm-builtin{color:#cda869}.cm-s-twilight .cm-tag{color:#997643}.cm-s-twilight .cm-attribute{color:#d6bb6d}.cm-s-twilight .cm-header{color:#ff6400}.cm-s-twilight .cm-hr{color:#aeaeae}.cm-s-twilight .cm-link{color:#ad9361;font-style:italic;text-decoration:none}.cm-s-twilight .cm-error{border-bottom:1px solid red}.cm-s-twilight .CodeMirror-activeline-background{background:#27282e}.cm-s-twilight .CodeMirror-matchingbracket{color:#fff!important;outline:1px solid grey}",""]);const o=i},645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r=e(t);return t[2]?"@media ".concat(t[2]," {").concat(r,"}"):r})).join("")},t.i=function(e,r,n){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(n)for(var o=0;o<this.length;o++){var a=this[o][0];null!=a&&(i[a]=!0)}for(var l=0;l<e.length;l++){var s=[].concat(e[l]);n&&i[s[0]]||(r&&(s[2]?s[2]="".concat(r," and ").concat(s[2]):s[2]=r),t.push(s))}},t}},158:()=>{},937:()=>{},379:(e,t,r)=>{"use strict";var n,i=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},o=function(){var e={};return function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}e[t]=r}return e[t]}}(),a=[];function l(e){for(var t=-1,r=0;r<a.length;r++)if(a[r].identifier===e){t=r;break}return t}function s(e,t){for(var r={},n=[],i=0;i<e.length;i++){var o=e[i],s=t.base?o[0]+t.base:o[0],c=r[s]||0,u="".concat(s," ").concat(c);r[s]=c+1;var d=l(u),f={css:o[1],media:o[2],sourceMap:o[3]};-1!==d?(a[d].references++,a[d].updater(f)):a.push({identifier:u,updater:g(f,t),references:1}),n.push(u)}return n}function c(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var i=r.nc;i&&(n.nonce=i)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var u,d=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function f(e,t,r,n){var i=r?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=d(t,i);else{var o=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(o,a[t]):e.appendChild(o)}}function p(e,t,r){var n=r.css,i=r.media,o=r.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),o&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var h=null,m=0;function g(e,t){var r,n,i;if(t.singleton){var o=m++;r=h||(h=c(t)),n=f.bind(null,r,o,!1),i=f.bind(null,r,o,!0)}else r=c(t),n=p.bind(null,r,t),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(r)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else i()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=i());var r=s(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<r.length;n++){var i=l(r[n]);a[i].references--}for(var o=s(e,t),c=0;c<r.length;c++){var u=l(r[c]);0===a[u].references&&(a[u].updater(),a.splice(u,1))}r=o}}}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={id:e,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.exports}n.m=t,e=[],n.O=(t,r,i,o)=>{if(!r){var a=1/0;for(u=0;u<e.length;u++){for(var[r,i,o]=e[u],l=!0,s=0;s<r.length;s++)(!1&o||a>=o)&&Object.keys(n.O).every((e=>n.O[e](r[s])))?r.splice(s--,1):(l=!1,o<a&&(a=o));if(l){e.splice(u--,1);var c=i();void 0!==c&&(t=c)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[r,i,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={138:0,118:0,766:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var i,o,[a,l,s]=r,c=0;if(a.some((t=>0!==e[t]))){for(i in l)n.o(l,i)&&(n.m[i]=l[i]);if(s)var u=s(n)}for(t&&t(r);c<a.length;c++)o=a[c],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(u)},r=self.webpackChunk=self.webpackChunk||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,n.O(void 0,[118,766],(()=>n(830))),n.O(void 0,[118,766],(()=>n(158)));var i=n.O(void 0,[118,766],(()=>n(937)));i=n.O(i)})();