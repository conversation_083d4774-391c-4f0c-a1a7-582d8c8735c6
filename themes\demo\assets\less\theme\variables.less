//
// Variables file
//
// Space for any custom variables used by this application
//

// Assets
// --------------------------------------------------
// For second level entry paths (pages, layouts, etc.)
@assets-url: "../../../assets";

// Brands
// --------------------------------------------------
@brand-primary: #3097d1;
@brand-info: #8eb4cb;
@brand-success: #4eb76e;
@brand-warning: #cbb956;
@brand-danger:  #bf5329;
@brand-accent: #6A6CF7;

@gray-base: #000;
@gray-darker: lighten(@gray-base, 13.5%); // #222
@gray-dark: lighten(@gray-base, 20%);   // #333
@gray: lighten(@gray-base, 33.5%); // #555
@gray-light: lighten(@gray-base, 46.7%); // #777
@gray-lighter: lighten(@gray-base, 93.5%); // #eee

// Typography
// --------------------------------------------------
@font-family-sans-serif: "lato", sans-serif;
@line-height-base: 1.7;
@font-size-base: 16px;
@font-size-h3: 18px;
@text-color: #586667;
@headings-color: #2C3E4F;
@headings-font-weight: bold;
@jumbotron-heading-color: @headings-color;
@border-radius-base: 4px;

@code-color: #D35400;
@code-bg: #ECF0F1;

// Breakpoints
// --------------------------------------------------

// Extra small screen
@screen-xs-min: 576px;

// Small screen / tablet
@screen-sm-min: 768px;

// Medium screen / desktop
@screen-md-min: 992px;

// Large screen / wide desktop
@screen-lg-min: 1200px;

// Extra large screen
@screen-xl-min: 1400px;

// So media queries don't overlap when required, provide a maximum
@screen-xs-max: (@screen-sm-min - 1);
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);
@screen-lg-max: (@screen-xl-min - 1);

// Jumbotron
// --------------------------------------------------

@jumbotron-padding: 40px;
@jumbotron-bg: transparent;
@jumbotron-color: #34495E;
@jumbotron-heading-color: @headings-color;
@jumbotron-font-size: 20px;
@jumbotron-heading-font-size: 65px;

// Spacing
// --------------------------------------------------
@spacer: 20px;
@spacer-y: @spacer;
@spacer-x: @spacer;

// Navbar
// --------------------------------------------------
@navbar-inverse-bg: #DB6A26;
@navbar-inverse-link-color: rgba(255,255,255,0.6);

@navbar-inverse-stripe-color-active: #ffffff;
@navbar-inverse-stripe-color-hover: #e67e22;
@navbar-default-stripe-color-active: #64ae5b;
@navbar-default-stripe-color-hover: #93dc8a;
@navbar-inverse-toggle-border-color: #ffffff;
@navbar-inverse-toggle-hover-bg: rgba(255, 255, 255, 0.3);

@navbar-padding-horizontal: 0;

// Callouts
// --------------------------------------------------
@callout-padding: 20px;
@callout-border-radius: @border-radius-base;
@callout-border: @gray-lighter;

@callout-info-bg: #f4f8fa;
@callout-info-text: #31708f;
@callout-info-border: darken(spin(@callout-info-bg, -10), 7%);

@callout-warning-bg: #faf8f0;
@callout-warning-text: #8a6d3b;
@callout-warning-border: darken(spin(@callout-warning-bg, -10), 5%);

@callout-danger-bg: #fdf7f7;
@callout-danger-text: #a94442;
@callout-danger-border: darken(spin(@callout-danger-bg, -10), 5%);

@callout-success-bg: #f9fdf7;
@callout-success-text: #3c763d;
@callout-success-border: darken(spin(@callout-success-bg, -10), 5%);

// Forms
// --------------------------------------------------
@input-border-color: #D7D7D7;
