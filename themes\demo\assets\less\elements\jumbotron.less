//
// Jumbotron
// --------------------------------------------------

.jumbotron {
    padding-top: @jumbotron-padding;
    padding-bottom: @jumbotron-padding;
    margin-bottom: @jumbotron-padding;
    color: @jumbotron-color;
    background-color: @jumbotron-bg;

    h1,
    .h1 {
        color: @jumbotron-heading-color;
        position: relative;
        padding-top: 66px;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 46px;
            height: 46px;
            background-size: 46px 46px!important;
        }
    }

    p {
        margin-top: 60px;
        margin-bottom: (@jumbotron-padding / 2);
        font-size: @jumbotron-font-size;
        font-weight: normal;
    }

    > hr {
        border-top-color: darken(@jumbotron-bg, 10%);
    }

    .container &,
    .container-fluid & {
        border-radius: @border-radius-base;
        padding-left: 15px;
        padding-right: 15px;
    }

    @media screen and (min-width: @screen-sm-min) {
        padding-top: (@jumbotron-padding * 1.6);
        padding-bottom: (@jumbotron-padding * 1.6);

        .container &,
        .container-fluid & {
            padding-left:  (@jumbotron-padding * 2);
            padding-right: (@jumbotron-padding * 2);
        }

        h1,
        .h1 {
            font-size: @jumbotron-heading-font-size;
        }
    }
}
