==
<?
// How to activate this form:
//
// - 1. Open Admin Panel → Settings → Team → Manage Groups
// - 2. Create a New Group → Set Code field to "contact-team"
// - 3. Select Users tab → Click Add Users
// - 4. Click Save
//
function onSubmitContactForm()
{
    // Validate the form
    // @see https://docs.octobercms.com/3.x/cms/features/validation.html
    //
    // - Argument 1 is the attributes and their rules
    // - Argument 2 is custom error messages for each attribute rule (translated)
    // - Argument 3 is custom names for each attribute (translated)
    //
    $data = Request::validate([
        'first_name' => 'required|min:2|max:64',
        'email' => 'required|email|min:2|max:64',
        'comments' => 'required|min:5',
    ], [
        'comments' => ['required' => __("Please actually write something to us...")]
    ], [
        'first_name' => __("name"),
    ]);

    // Notify the 'contact-team' group with 'backend:contact-form' mail template
    //
    $group = \Backend\Models\UserGroup::where('code', 'contact-team')->first();
    if (!$group) {
        throw new ApplicationException(__("Sorry, this contact form is not enabled: We cannot receive emails at the moment, please try using another method."));
    }

    Mail::sendTo(
        $group->users->pluck('full_name', 'email')->all(),
        'backend:contact-form',
        $data
    );

    $this['isSubmit'] = true;
    $this['emailAddress'] = post('email');
}
?>
==
<div class="contact-us-form">
    <h3 class="mb-4">Drop Us a Line</h3>
    <p class="lead">Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

    {% if isSubmit %}
        <div class="alert alert-success text-start" role="alert">
            <h4 class="alert-heading">Thanks for getting in touch!</h4>
            <p>Please bear in mind we receive a lot of feedback, it may take several days to process and you may not receive a response.</p>
            <p class="mb-0">Not to worry though, we have your email <strong>{{ emailAddress }}</strong>, all feedback we receive will be read and action taken if needed.</p>
        </div>
    {% else %}
        <form
            data-request="onSubmitContactForm"
            data-request-update="{ _self: true }"
            data-request-flash>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        <input type="text" name="first_name" class="form-control" id="commentNameInput">
                        <label for="commentNameInput">First Name</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        <input type="text" name="last_name" class="form-control" id="commentLastNameInput">
                        <label for="commentLastNameInput">Last Name</label>
                    </div>
                </div>
            </div>
            <div class="form-floating mb-3">
                <input type="email" name="email" class="form-control" id="commentEmailInput">
                <label for="commentEmailInput">Email address</label>
            </div>
            <div class="mb-3 form-floating">
                <textarea name="comments" style="height:125px" class="form-control" id="commentContentTextarea"></textarea>
                <label for="commentContentTextarea">Comment</label>
            </div>
            <div class="form-buttons d-flex pt-2">
                <div>
                    <button
                        type="submit"
                        data-attach-loading
                        class="btn btn-primary btn-lg btn-pill">
                        Submit
                    </button>
                </div>
            </div>
        </form>
    {% endif %}
</div>
