.navbar {
    padding-top: 15px;
    padding-bottom: 15px;

    &.navbar-dark {
        background-color: transparent;
    }

    .navbar-brand {
        margin-top: -5px;
    }

    a:hover, a:focus, a.focus {
        text-decoration: none;
    }

    .dropdown-item.active, .dropdown-item:active {
        background-color: #6bc48d;
    }
}

@media screen and (min-width: @screen-md-min) {
    .navbar {
        .navbar-nav > li.nav-item {
            padding: 0 8px;

            > a.btn {
                padding: 3px 22px;
                border-radius: 100px;
                font-size: 14px;
                margin-top: 7px;
            }

            > a.nav-link {
                position: relative;
                transition: color 0.2s ease 0.05s;
                color: #fff;

                &:before {
                    position: absolute;
                    height: 4px;
                    bottom: 2px;
                    content: '';
                    border-radius: 4px;
                    z-index: 5;
                    width: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    transition: all 0.2s ease 0.05s;
                }

                &.active:before,
                &.active:hover:before {
                    background: #fff;
                }

                &:hover:before {
                    background: #fff;
                }
            }
        }
    }
}

// Mobile Nav
.navbar-mobile {
    display: none;
}

@media (max-width: @screen-sm-max) {
    .navbar-mobile {
        display: block;

        .navbar-collapse {
            background: #2d3134;
            position: fixed;
            z-index: 10001;
            right: -260px;
            top: 0;
            bottom: 0;
            width: 260px;
            padding: 20px;
            height: 100% !important;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            text-align: left;
            backface-visibility: hidden;
            transform: translate3d(0, 0, 0);
            transform-origin: 0 10%;
            transform: perspective(1000px) scale(1.3);
            transition: all 0.4s 0s ease-in;

            &.collapsing {
                transition-duration: 0.1s;
            }

            &.show {
                transition: all 0.3s 0s ease-out;
                transform: perspective(1000px) scale(1) translate3d(-260px, 0, 0);
            }
        }

        .navbar-toggler {
            color: #fff;
            padding: 10px;
            opacity: .8;

            &:hover, &:focus {
                opacity: 1;
            }
        }

        .nav-item {
            .nav-link {
                color: #e0e0e0;
                &:hover {
                    color: #fff;
                }
            }
            .btn {
                margin-top: 1rem;
                margin-left: 1rem;
            }
        }
    }
}
