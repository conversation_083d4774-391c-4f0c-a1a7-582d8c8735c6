##
url = "/wiki"
layout = "wiki"
title = "Wiki Docs Demo"
meta_title = "Wiki"

[collection wiki]
handle = "Page\Article"

[resources]
vars[activeNavLink] = 'wiki'
==
{% set article = wiki.first() %}

<div class="container">
    <article class="wiki-article pb-3">
        <h1 class="wiki-article-title mb-3">{{ article.title }}</h1>
        <p class="lead">{{ article.summary_text }}</p>

        <div class="wiki-banner mb-3">
            {% if article.banner %}
                <div class="text-banner" style="background-image:url('{{ article.banner.path }}')"></div>
            {% else %}
                <div class="text-banner" style="background-image:url('{{ partial('site/helpers/random-stock-image') }}')"></div>
            {% endif %}
        </div>

        {{ article.content|raw }}
    </article>

    {% if article.external_links %}
        <hr />
        <h2>External Links</h2>
        <ul class="bullet-list list-content">
            {% for link in article.external_links %}
                <li><a href="{{ link.link_url }}">{{ link.link_text }}</a></li>
            {% endfor %}
        </ul>
    {% endif %}
</div>
