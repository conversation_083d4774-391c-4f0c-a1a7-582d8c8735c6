{% macro render_breadcrumb(article) %}
    {% if article.parent %}
        {{ _self.render_breadcrumb(article.parent) }}
        <li class="breadcrumb-item">
            <a href="{{ 'wiki/article'|page({ fullslug: article.parent.fullslug, id: article.parent.id }) }}">
                {{ article.parent.title }}
            </a>
        </li>
    {% endif %}
{% endmacro %}

{% import _self as nav %}

<nav aria-label="breadcrumb">
    <ol class="breadcrumb mb-0">
        {{ nav.render_breadcrumb(article) }}
        <li class="breadcrumb-item active" aria-current="page">{{ article.title }}</li>
    </ol>
</nav>
