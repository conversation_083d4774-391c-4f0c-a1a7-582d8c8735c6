//
// Common Styles
//

// BS vars
:root {
    --bs-body-line-height: 1.7;
    --bs-body-color: #343F52;
}

a {
    color: #3097d1;
    text-decoration: none;
}

a:hover, a:focus {
    color: #216a94;
    text-decoration: underline;
}

h1, .h1 {
    font-size: 40px;
}
h2, .h2 {
    font-size: 26px;
}
h3, .h3 {
    font-size: 22px;
}
h4, .h4 {
    font-size: 19px;
}
h5, .h5 {
    font-size: 16px;
}
h6, .h6 {
    font-size: 14px;
}

h1, .h1, h2, .h2 {
    font-weight: 700;
}

h1, .h1, h2, .h2, h3, .h3 {
    margin-bottom: 13.5px;
}

p.lead {
    font-size: 20px;
    font-weight: 400;
}

code {
    padding: 2px 4px;
    font-size: 90%;
    color: #D35400;
    background-color: #ECF0F1;
    border-radius: 4px;
}

// Disable collapse animation
.collapsing {
    transition: none;
}
