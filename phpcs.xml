<?xml version="1.0"?>
<ruleset name="October CMS">
    <description>The coding standard for October CMS.</description>
    <rule ref="PSR2">
        <!--
        Exceptions to the PSR-2 guidelines as per our Developer Guide:
        https://octobercms.com/help/guidelines/developer#psr-exceptions
        -->
        <exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps" />
        <exclude name="PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace" />
        <exclude name="Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace" />
        <exclude name="Squiz.Functions.MultiLineFunctionDeclaration.SpaceAfterFunction"/>
        <exclude name="Generic.Files.LineLength"/>
    </rule>

    <rule ref="PSR1.Classes.ClassDeclaration.MissingNamespace">
        <!-- Migration files and tests do not need a namespace defined -->
        <exclude-pattern>*/database/migrations/*\.php</exclude-pattern>
    </rule>

    <file>app/</file>
    <file>config/</file>
    <file>modules/</file>
    <file>plugins/october/demo/</file>

    <!-- Ignore vendor files -->
    <exclude-pattern>*/vendor/*</exclude-pattern>

    <!-- Ignore tests -->
    <exclude-pattern>*/modules/*/tests/*</exclude-pattern>

    <!-- Ignore views -->
    <exclude-pattern>*/modules/*/views/*</exclude-pattern>

    <!-- Ignore backend UI -->
    <exclude-pattern>*/modules/*/elements/*</exclude-pattern>

    <!-- Ignore controllers, layouts -->
    <exclude-pattern>*/modules/*/layouts/*</exclude-pattern>
    <exclude-pattern>*/modules/*/controllers/*/*</exclude-pattern>

    <!-- Ignore partial views -->
    <exclude-pattern>*/modules/**/_*</exclude-pattern>
</ruleset>
