{% set galleryId = 'carousel' ~ random() %}
{% set width = width|default(640) %}
{% set height = height|default(640) %}

<div data-control="gallery-slider" class="control-gallery-slider" id="{{ galleryId }}">
    {% if gallery %}
        {% for image in gallery %}
            <div>
                <div class="image-container">
                    <a href="{{ image|media }}">
                        <img src="{{ image|resize(width, height, { extension: 'png', mode: 'crop' }) }}">
                    </a>
                    <div class="pswp-caption-content">
                        <h3>{{ image.title }}</h3>
                        <p>{{ image.description }}</p>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        {% for defaultImage in ['workspace', 'desktop', 'pancakes', 'doughnuts'] %}
            {% set image = ('assets/images/stock/' ~ defaultImage ~ '.png')|theme %}
            <div>
                <div class="image-container">
                    <a href="{{ image }}">
                        <img src="{{ image|resize(width, height, { extension: 'png', mode: 'crop' }) }}">
                    </a>
                </div>
            </div>
        {% endfor %}
    {% endif %}
</div>
