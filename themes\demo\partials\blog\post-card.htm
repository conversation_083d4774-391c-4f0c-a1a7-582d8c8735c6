<div class="col {{ cssClass|default('') }}">
    <div class="card card-post mb-5">
        {% if post.banner %}
            <div class="card-banner {{ bannerCss|default('') }}" style="background-image:url('{{ post.banner.path }}')"></div>
        {% else %}
            <div class="card-banner {{ bannerCss|default('') }}" style="background-image:url('{{ partial('site/helpers/random-stock-image') }}')"></div>
        {% endif %}

        <div class="card-body">
            {% if post.categories %}
                <div class="blog-post-categories">
                    <ul class="list-inline">
                        {% for category in post.categories %}
                            <li class="list-inline-item">
                                &mdash; <a href="{{ 'blog/category'|page({ slug: category.slug, id: category.id }) }}">{{ category.title }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}

            <h4 class="card-title">
                <a href="{{ 'blog/post'|page({ slug: post.slug, id: post.id }) }}" class="stretched-link">{{ post.title }}</a>
            </h4>

            <div class="featured-text">
                <p>{{ post.featured_text }}</p>
            </div>
        </div>

        <div class="card-footer">
            <div class="card-meta">
                <div class="meta-item meta-date text-icon text-icon-date">
                    {{ post.published_at_date|date('j M Y') }}
                </div>
                {% if post.author %}
                    <div class="meta-item meta-divider">
                        &bull;
                    </div>
                    <div class="meta-item meta-author text-icon text-icon-author">
                        By <a href="{{ 'blog/author'|page({ slug: post.author.slug }) }}">
                            {{ post.author.title|default('') }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
