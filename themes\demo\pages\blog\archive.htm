##
url = "/blog/archive/:year|(^[0-9]{4}$)/:month|(^0?[1-9]$)|(^1[0-2]$)"
title = "Blog Archive"
layout = "blog"

[collection blog]
handle = "Blog\Post"

[resources]
vars[activeNavLink] = 'blog'
==
{% set dateParsed = date('1-'~this.param.month~'-'~this.param.year) %}
{% set posts = blog
    .where('published_at_year', this.param.year)
    .where('published_at_month', this.param.month)
    .get()
%}
{% put pageTitle = 'Articles from ' ~ dateParsed|date('F Y') %}

<div class="blog-featured row row-cols-1 row-cols-xl-2 g-3 pb-5">
    {% for post in posts %}
        {% partial 'blog/post-card' post=post %}
    {% endfor %}
</div>
