##
description = "Wiki layout"
==
<!DOCTYPE html>
<html>
    <head>
        {% partial 'site/head/head-meta' %}
        <title>October CMS - {{ this.page.meta_title ?: placeholder('pageTitle') }}</title>
        <meta name="description" content="{{ this.page.meta_description }}">
        <meta name="title" content="{{ this.page.meta_title }}">
        {% partial 'site/head/head-links' %}
        {% partial 'site/head/head-scripts' %}
        {% partial 'site/head/analytics-code' %}
    </head>
    <body class="layout-wiki">

        <!-- Header -->
        <header id="layout-header">
            {% partial 'site/header' %}
        </header>

        {% partial 'site/flash-messages' %}

        <!-- Content -->
        <section id="layout-content">
            <div class="container">
                <div class="row g-5">
                    <div class="col-md-4">
                        <div class="position-sticky" style="top: 2rem;">
                            {% partial 'wiki/sidebar' %}
                        </div>
                    </div>
                    <div class="col-md-8">
                        {% page %}
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer id="layout-footer">
            {% partial 'site/footer' %}
        </footer>

        <!-- Mobile -->
        {% partial 'site/nav-mobile' %}

        <!-- How the page is made -->
        {% partial 'site/how-its-made' %}

    </body>
</html>
