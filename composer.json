{"name": "october/october", "description": "Built using October CMS: The Laravel-Based CMS Engineered For Simplicity", "type": "project", "homepage": "https://octobercms.com", "license": "proprietary", "require": {"php": "^8.0.2", "october/rain": "^3.7", "laravel/framework": "^9.0"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.0"}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-autoload-dump": ["System\\Console\\ComposerScript::postAutoloadDump"], "post-update-cmd": ["System\\Console\\ComposerScript::postUpdateCmd"], "pre-package-uninstall": ["System\\Console\\ComposerScript::prePackageUninstall"], "test": ["phpunit --stop-on-failure"]}, "config": {"preferred-install": "dist", "allow-plugins": {"composer/installers": true}}, "autoload": {"psr-4": {"App\\": "app", "System\\Console\\": "modules/system/console"}}, "minimum-stability": "stable", "prefer-stable": true}