.card {
    border-radius: 13px;
    box-shadow: 0px 0px 22px rgba(0, 0, 0, 0.07);
    border-color: #EBEBEB;
    overflow: hidden;

    .card-banner {
        width: 100%;
        height: 191px;
        background-position: center center;
        background-size: cover;

        &.banner-sm {
            height: 120px;
        }

        &.banner-lg {
            height: 268px;
        }
    }

    .card-divider {
        padding: 1.5rem;

        &:after {
            content: '';
            border-bottom: 1px solid #EBEBEB;
            display: block;
        }
    }

    .card-body {
        padding: 1.5rem;

        &.card-lg {
            padding-right: 2.5rem;
            padding-left: 2.5rem;
        }
    }

    .card-footer {
        background-color: #fff;
        padding: 1rem 1.5rem;
        border-bottom-left-radius: 13px;
        border-bottom-right-radius: 13px;
    }

    .card-title {
        a {
            color: #000;
            text-decoration: none;
        }
    }

    .card-links {
        position: relative;
        z-index: 2;
    }
}

.card-post {
    &.card-primary {
        margin-bottom: -25px;
        position: relative;
        z-index: 3;
    }

    .featured-text {
        p:last-child {
            margin-bottom: 0;
        }
    }

    .share-button {
        margin-top: -5px;
    }

    .card-meta {
        .meta-item {
            display: inline-block;
            position: relative;
            color: #A2A2A2;
            font-size: 14px;
        }

        .meta-divider {
            width: 20px;
            text-align: center;
        }
    }
}

