// Extend footer to the entire page
body, .element-footer {
    background: linear-gradient(97.23deg, #2D8BFF -7.32%, #9F74FB 106.79%);
}

.element-footer {
    position: relative;
    overflow: hidden;
    min-height: 298px;
    padding-top: 70px;
    z-index: 1;

    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 106px;
        background: url('@{assets-url}/images/waves/footer-wave.svg') repeat-x 0 0;
        background-repeat: repeat-x;
        z-index: 1;
        top: -1px;
    }

    &.footer-bluezone:before {
        background: url('@{assets-url}/images/waves/footer-blue-wave.svg') repeat-x 0 0;
    }

    > .container {
        position: relative;
        padding: 30px 0;
        color: #fff;
        z-index: 2;
    }

    // Decorations
    .footer-decoration-1 {
        .decoration-circle();
        width: 524px;
        height: 524px;
        left: -42px;
        top: 120px;
        opacity: .02;
    }

    .footer-decoration-2 {
        .decoration-circle();
        width: 524px;
        height: 524px;
        right: -150px;
        top: -160px;
        opacity: .05;
    }

    .footer-nav {
        padding-bottom: 22px;

        .nav {
            padding-right: 50px;

            .nav-item {
                font-size: 16px;

                &.nav-item-header > a {
                    font-weight: 700;
                }

                > a {
                    color: #fff;
                    padding: 4px 0;
                }
            }
        }
    }

    .footer-brand {
        padding: 32px 0;
    }

    .footer-social .nav {
        .nav-item {
            &:first-child > a {
                padding-left: 0;
            }

            img {
                height: 28px;
            }
        }
    }

    .footer-copyright {
        text-align: right;
        p {
            margin: 0;
            padding: 0;
            line-height: 28px;
        }
    }

    @media (max-width: @screen-sm-max) {
        padding-left: 20px;
        padding-right: 20px;
    }
}
